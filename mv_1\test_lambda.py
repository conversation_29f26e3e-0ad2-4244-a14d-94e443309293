#!/usr/bin/env python3
"""
Test script for the document processing Lambda function.
Demonstrates all the implemented strategies for handling large JSON data.
"""

import json
import sys
import os

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from index import lambda_handler


def test_basic_processing():
    """Test basic document processing with default settings"""
    print("=" * 60)
    print("TEST 1: Basic Document Processing")
    print("=" * 60)
    
    event = {
        "document_type": "invoice",
        "processing_mode": "all"
    }
    
    result = lambda_handler(event, None)
    
    print(f"Status Code: {result['statusCode']}")
    print(f"Total Chunks: {result['body']['processing_summary']['total_chunks']}")
    print(f"Pages Processed: {result['body']['processing_summary']['pages_processed']}")
    print(f"Words Processed: {result['body']['processing_summary']['total_words']}")
    
    return result


def test_chunking_strategy():
    """Test document chunking with different page limits"""
    print("\n" + "=" * 60)
    print("TEST 2: Document Chunking Strategy")
    print("=" * 60)
    
    # Test with smaller chunks
    event = {
        "document_type": "invoice",
        "processing_mode": "chunked",
        "max_pages_per_chunk": 3,  # Smaller chunks to demonstrate chunking
        "confidence_threshold": 0.8
    }
    
    result = lambda_handler(event, None)
    
    print(f"Max Pages Per Chunk: {event['max_pages_per_chunk']}")
    print(f"Total Chunks Created: {result['body']['processing_summary']['total_chunks']}")
    
    # Show chunk details
    for i, chunk in enumerate(result['body']['chunks'][:3]):  # Show first 3 chunks
        print(f"  Chunk {i+1}: Pages {chunk['pages']}, Words: {chunk['word_count']}")
    
    return result


def test_confidence_filtering():
    """Test confidence-based filtering"""
    print("\n" + "=" * 60)
    print("TEST 3: Confidence Filtering")
    print("=" * 60)
    
    # Test with high confidence threshold
    event = {
        "document_type": "invoice",
        "processing_mode": "filtered",
        "confidence_threshold": 0.9  # High threshold to filter out more words
    }
    
    result = lambda_handler(event, None)
    
    summary = result['body']['processing_summary']
    print(f"Confidence Threshold: {summary['confidence_threshold']}")
    print(f"Original Words: {summary['total_words']}")
    print(f"Filtered Words: {summary['filtered_words']}")
    print(f"Reduction: {((summary['total_words'] - summary['filtered_words']) / summary['total_words'] * 100):.1f}%")
    
    return result


def test_multiple_data_formats():
    """Test different data format outputs"""
    print("\n" + "=" * 60)
    print("TEST 4: Multiple Data Formats")
    print("=" * 60)
    
    event = {
        "document_type": "title",
        "processing_mode": "all",
        "max_pages_per_chunk": 5
    }
    
    result = lambda_handler(event, None)
    
    # Examine first chunk's formats
    first_chunk = result['body']['chunks'][0]
    formats = first_chunk['formats']
    
    print("Available Data Formats:")
    for format_name, format_data in formats.items():
        if isinstance(format_data, list):
            print(f"  {format_name}: {len(format_data)} items")
            if format_data and isinstance(format_data[0], dict):
                print(f"    Sample keys: {list(format_data[0].keys())}")
        else:
            print(f"  {format_name}: {len(str(format_data))} characters")
    
    return result


def test_page_range_processing():
    """Test processing specific page ranges"""
    print("\n" + "=" * 60)
    print("TEST 5: Page Range Processing")
    print("=" * 60)
    
    event = {
        "document_type": "bol",
        "processing_mode": "all",
        "page_range": [2, 4],  # Process only pages 2-4
        "max_pages_per_chunk": 2
    }
    
    result = lambda_handler(event, None)
    
    summary = result['body']['processing_summary']
    print(f"Requested Page Range: {event['page_range']}")
    print(f"Pages Processed: {summary['pages_processed']}")
    print(f"Total Chunks: {summary['total_chunks']}")
    
    # Show which pages were actually processed
    all_pages = []
    for chunk in result['body']['chunks']:
        all_pages.extend(chunk['pages'])
    print(f"Actual Pages Processed: {sorted(set(all_pages))}")
    
    return result


def test_error_handling():
    """Test error handling and context limit simulation"""
    print("\n" + "=" * 60)
    print("TEST 6: Error Handling & Context Limits")
    print("=" * 60)
    
    event = {
        "document_type": "invoice",
        "processing_mode": "all",
        "max_pages_per_chunk": 20,  # Large chunks to potentially trigger context limits
        "confidence_threshold": 0.5
    }
    
    result = lambda_handler(event, None)
    
    # Check for any error handling in chunks
    error_count = 0
    fallback_count = 0
    success_count = 0
    
    for chunk in result['body']['chunks']:
        if 'llm_processing' in chunk:
            status = chunk['llm_processing']['status']
            if status == 'error':
                error_count += 1
            elif status == 'fallback':
                fallback_count += 1
            elif status == 'success':
                success_count += 1
    
    print(f"Processing Results:")
    print(f"  Successful: {success_count}")
    print(f"  Fallback (context limit): {fallback_count}")
    print(f"  Errors: {error_count}")
    
    return result


def test_extraction_templates():
    """Test different document types and their extraction templates"""
    print("\n" + "=" * 60)
    print("TEST 7: Extraction Templates")
    print("=" * 60)
    
    document_types = ["invoice", "title", "bol"]
    
    for doc_type in document_types:
        event = {
            "document_type": doc_type,
            "processing_mode": "all",
            "max_pages_per_chunk": 5
        }
        
        result = lambda_handler(event, None)
        template = result['body']['extraction_template']
        
        print(f"\n{doc_type.upper()} Template Fields:")
        for field, description in template.items():
            print(f"  {field}: {description[:50]}...")


def run_comprehensive_test():
    """Run all tests and provide summary"""
    print("🚀 Starting Comprehensive Document Processing Tests")
    print("Testing all implemented strategies for handling large JSON data\n")
    
    try:
        # Run all tests
        test_basic_processing()
        test_chunking_strategy()
        test_confidence_filtering()
        test_multiple_data_formats()
        test_page_range_processing()
        test_error_handling()
        test_extraction_templates()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS COMPLETED SUCCESSFULLY")
        print("=" * 60)
        
        print("\n📋 IMPLEMENTED STRATEGIES SUMMARY:")
        print("✓ Document Chunking: Max 10 pages per LLM call")
        print("✓ Multiple Data Formats:")
        print("  - raw_text_ocr (with IDs) - for precise extraction")
        print("  - raw_text_plain (without IDs) - for smaller payload")
        print("  - raw_text_with_coords (with coordinates) - for spatial analysis")
        print("✓ Error Handling: Graceful fallback when context limits are hit")
        print("✓ JSON Template Embedding: Schema is part of prompt, not message data")
        print("✓ Confidence Filtering: Remove low-quality OCR text to reduce size")
        print("✓ Batch Processing: Process document chunks sequentially")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_comprehensive_test()
