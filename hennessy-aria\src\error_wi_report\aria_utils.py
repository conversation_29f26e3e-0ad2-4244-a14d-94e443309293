import requests
import time
from boto3_utils import get_aria_credentials

class TemporalUnavailableException(Exception):
    """Exception for handling temporary unavailability in API responses."""
    def __init__(self, message, response=None):
        super().__init__(message)
        self.response = response

class AriaClient:
    def __init__(self, app_id, app_uuid):
        self.credentials = get_aria_credentials()
        self.app_id = app_id
        self.app_uuid = app_uuid
        self.headers = {
            'Authorization': f"{self.credentials['token']}",
            'Content-Type': 'application/json'
        }
        self.aria_base_url = f"{self.credentials['url']}/public/v1/apps/{self.app_uuid}/case_management_middleware"
        self.aria_app_url = f"{self.credentials['url']}/applications/{self.app_id}"
        self.aria_url = f"{self.credentials['url']}/"
        
    def retrieve_connector_response(self, response_url, max_retries=10):
        """
        Polls ARIA until a 200 response is received or max retries are reached.
        """
        for attempt in range(1, max_retries + 1):
            response = requests.get(response_url, headers=self.headers)
            print(f"Attempt {attempt}: Received response status {response.status_code} from connector.")

            if response.status_code == 200:
                try:
                    return response.json()
                except requests.exceptions.JSONDecodeError:
                    print("Warning: Expected JSON but received a non-JSON response. Response content:")
                    return {}

            elif response.status_code == 202:
                print(f"Received 202 status, waiting for completion... (Attempt {attempt})")
                time.sleep(2 * attempt)  # Exponential backoff
            else:
                print(f"Unexpected status code {response.status_code}: {response.text}")
                response.raise_for_status()

        raise TemporalUnavailableException("Failed to retrieve connector response after max retries")

    def post_workitem_status_update(self, workitem_id, aria_status_value):
        """
        Updates the status of a work item in ARIA.
        """
        url = f"{self.aria_base_url}/work_items/{workitem_id}/bre"
        body = {
            "data": {
                "type": "workItem",
                "id": workitem_id,
                "attributes": {
                    "response": {
                        "aria_status": {
                            "value": aria_status_value
                        }
                    }
                }
            }
        }
        print(f"Updating workitem {workitem_id} status with URL: {url} and body: {body}")
        response = requests.post(url, headers=self.headers, json=body)
        print(f"Received response status: {response.status_code} for workitem update.")
        
        if response.status_code == 200:
            print("Status updated immediately")
            return "Status updated immediately"
        elif response.status_code == 202:
            print("Received 202; waiting for completion...")
            completion_url = f"{self.aria_base_url}/work_items/{workitem_id}/connector_data"
            return self.retrieve_connector_response(completion_url)
        else:
            print(f"Error in updating workitem {workitem_id}: {response.status_code}")
            response.raise_for_status()
