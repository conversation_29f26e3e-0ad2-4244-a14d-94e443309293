import os
import sys
import time
import json
import requests

from datetime               import datetime, timedelta

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import invokeRPA
from Invoices.downloader import Downloader
from Invoices.config     import DefaultConfig

class GENERALMOTORS(Downloader):
    def __init__(self, store, stage):
        super().__init__(store, stage)
        self.chrome.navigate_to_url('https://www.autopartners.net/gmentsso/UI/Login?goto=https%3A%2F%2Fdealer.autopartners.net%3A443%2F')

        # Initialize VIN variable
        self.vin = ''
        self.label = 'gm'

    def login(self, user, password, retry=True):
        """
        Login to the Porsche portal.
        1. Enter credentials
        """
        self.stage = 'LOGIN'
        self.user = user
        self.password = password
        try:

            self.chrome.get_element('ID','IDToken1')
            # Login
            self.chrome.type_value('ID','IDToken1', user)
            self.chrome.type_value('ID','IDToken2', password, password=True)
            self.chrome.click_button('XPATH','//input[@name="Login.Submit"]')
            
            #self.chrome.navigate_to_url('https://www.autopartners.net/gmentsso/UI/Login?goto=https%3A%2F%2Fdealer.autopartners.net%3A443%2F', new_tab=True)
            time.sleep(1)
            self.chrome.get_element('XPATH','//input[@type="email" and @name="loginfmt"]')
            self.chrome.type_value('XPATH','//input[@type="email" and @name="loginfmt"]', f"{user}@vsp.autopartners.net")
            self.chrome.click_button('XPATH','//input[@type="submit"]')

            time.sleep(2)

            self.chrome.get_element('XPATH','//input[@type="password" and @name="passwd"]')
            self.chrome.type_value('XPATH','//input[@type="password" and @name="passwd"]', password)
            self.chrome.click_button('XPATH','//input[@type="submit"]')

            found = self.chrome.get_element("XPATH", '//span[contains(text(), "Global Connect") and @class="p-element"]')
            if found:
                self._log_info('Correctly logged')
            else:
                raise Exception("Couldnt get logged")

        except Exception as e:
            self._handle_error(e)


    def download_vins(self, vins):
        """
        Download invoices
        """
        self.navigate_to_search_menu()
        return super().download_vins(vins)

    def navigate_to_search_menu(self, retry=True):
        """Navigate to the search menu"""
        self.stage = 'SEARCH_MENU'
        try:
            self._log_info('Start')
            self.chrome.click_button("XPATH", '//span[contains(text(), "APP CENTER")]')


            # Click Sales dropdown
            self.chrome.get_element("XPATH", '//h1[contains(text(), " BARS Reprint Request ")]')
            self.chrome.click_button("XPATH", '//button[@aria-label="Launch BARS Reprint Request"]')

            # Click Vehicle Invoice option
            self.chrome.get_element("XPATH", '//a[contains(text(), "Veh Invoice")]')

            self.chrome.click_button("XPATH", '//a[contains(text(), "Veh Invoice")]')

            self._log_info('Accessed to the download vins site')
            self._log_info('Success')

        except Exception as e:
            self._handle_error(e)

    def search_by_vin(self, vin, count):
        """Search invoice"""
        self.stage = 'SEARCH_VIN'
        time.sleep(5)

        try:
            self.vin = vin
            if count > 1:
                self.chrome.switch_to_default()
                self.navigate_to_search_menu()
                self._log_info('Back to search menu')
                
            self.chrome.switch_frame("appLoadFrame")

            # Select VIN
            self.chrome.get_element("XPATH", "//input[@type='radio' and @value='Vin']")
            self.chrome.click_button("XPATH", "//input[@type='radio' and @value='Vin']")
            # Fill VIN 
            self.chrome.type_value("XPATH", "/html/body/form/table/tbody/tr[5]/td[2]/input", vin)
            
            # Click Submit
            self.chrome.click_button("XPATH", "//input[@type='button' and @value='Submit']")

            not_exists = self.chrome.get_element("XPATH", '//div[@id = "errMsg"]/b/font[contains(text(), "Invalid VIN")]')
            if not_exists != False:
                self._log_error(f'VIN: {vin} not found')
                return False

            
            self._log_info(f'{vin} found')
            return True
        
        except Exception as e:
            self._handle_error(e)

    def save_invoice(self):
        """Download invoice"""
        self.stage = 'SAVE_INVOICE'
        try:
            self._log_info('Start')

            self.chrome.switch_frame("appLoadFrame")
            exists_multiple = self.chrome.get_element("XPATH", "//input[@type='button' and @value='Print All Invoices']")
            if exists_multiple != False:
                self.chrome.click_button("XPATH", f'//table/tbody/tr[td/a/font[text()="{self.vin}"]][1]/td/input')
                self.chrome.run_script("checkPrintSelection();")
                self.chrome.switch_tab(1)
                self.chrome.save_html_as_pdf(f"/tmp/MBG/{self.vin}.pdf")
                

            else:
                # Click on print invoice
                self.chrome.run_script("printInvoice();")
                self.chrome.switch_tab(1)
                self.chrome.save_html_as_pdf(f"/tmp/MBG/{self.vin}.pdf")

            # Wait for the invoice to download
            self._log_info('Download started')
            self.get_file_from_download()
            self.chrome.close_window()
            self.chrome.switch_tab(0)


            self._log_info('Success')

        except Exception as e:
            self._handle_error(e)