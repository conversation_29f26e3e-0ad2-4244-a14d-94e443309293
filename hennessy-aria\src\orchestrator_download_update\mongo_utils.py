import pymongo

class Mongo():
    def __init__(self, mongo_uri):
        self.client = pymongo.MongoClient(mongo_uri)
        self.db = self.col = ''

    def select_db_and_collection(self, db_name, collection_name):
        self.db = self.client[db_name]
        self.col = self.db.get_collection(collection_name)

    def insert_one(self, data):
        self.col.insert_one(data)

    def insert_many(self, data):
        self.col.insert_many(data)

    def update_one(self, filter, data):
        self.col.update_one(filter, data)

    def update_many(self, filter, data):
        self.col.update_many(filter, data)

    def find_one(self, data):
        x = self.col.find_one(data)
        return x
    
    def find(self, query):
        x = self.col.find(query)
        return x

    def find_one_and_update(self, filter, data):
        x = self.col.find_one_and_update(filter, data, return_document=True)
        return x
    
    def select_distinct(self, query, field):
        x = self.col.distinct(field, query)
        return x

    def close_connection(self):
        self.client.close()
