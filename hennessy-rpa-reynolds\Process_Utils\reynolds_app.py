from Utilities.Invoke_RPA.invoke_rpa import Invoke_RPA
from Utilities.Common_Utilities.logger_utility import Logger
from Utilities.Common_Utilities.gui_utility import Gui_utilities
import os
import subprocess
import time
from datetime import datetime
import pyperclip
import ssl
import ipdb
import pya<PERSON>gui

def get_screenshot():
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")        
    screenshot = pyautogui.screenshot()
    filename = f"{os.path.expandvars(r"%USERPROFILE%\Downloads\image_exception_logs")}/screenshot_{timestamp}.png"
    screenshot.save(filename)
    return filename

class PopUpException(Exception):
    def __init__(self, message, code, screenshot_path = None):
        super().__init__(message)
        self.code = code
        self.screenshot = get_screenshot()
        if screenshot_path is None:
            self.screenshot = get_screenshot()

class PostedVehicleException(Exception):
    def __init__(self, message, code, screenshot_path = None):
        super().__init__(message)
        self.code = code
        self.screenshot = get_screenshot()
        if screenshot_path is None:
            self.screenshot = get_screenshot()

class VinDataException(Exception):
    def __init__(self, message, code, screenshot_path = None):
        super().__init__(message)
        self.code = code
        self.screenshot = get_screenshot()
        if screenshot_path is None:
            self.screenshot = get_screenshot()

class UpdateReynoldsException(Exception):
    def __init__(self, message, code, screenshot_path = None):
        super().__init__(message)
        self.code = code
        if screenshot_path is None:
            self.screenshot = get_screenshot()

class ReynoldsApp():

    def __init__(self):
        self.logger = Logger(os.path.expandvars(r"%USERPROFILE%\Downloads\logs"))
        self.process = None

        tagui_path = os.path.expandvars(r"%USERPROFILE%\Downloads\tagui6")
        
        ssl._create_default_https_context = ssl._create_unverified_context

        self.invoke_rpa = Invoke_RPA(tagui_path, timeout=30, visual_automation=True, chrome_browser=False)
        self.gui_utilities = Gui_utilities(os.path.expandvars(r"%USERPROFILE%\Downloads\logs"))

    def open_reynolds(self):
        """
        Launch the ERA-IGNITE application by running the executable
        with specified arguments, and verify if it started correctly.
        """
        exe_path = r"C:\rey\Bin\pwrsuite.exe"
        arguments = ["ERAINIT0", "PRGINIT0"]

        try:
            # Launch the application using subprocess
            self.process = subprocess.Popen([exe_path] + arguments, shell=True)
            # Wait for the "User Sign-On" element to appear
            script_dir = os.path.dirname(os.path.abspath(__file__))
            image_path = os.path.join(script_dir, "elements", "signon.PNG")
            found = self.invoke_rpa.element_exists(image_path, 120)
            if found:
                self.logger.log("INFO", "ERA-IGNITE launched successfully.")
            else:
                self.logger.log("ERROR", "Error launching ERA-IGNITE: 'User Sign-On' not found.")
                raise Exception("ERROR WHEN LAUNCHING REYNOLDS")
        except Exception as e:
            self.logger.log("ERROR", f"Error launching ERA-IGNITE: {e}")

    def update_reynolds(self):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.logger.log("INFO", f"Checking if needed update reynolds")
        image_path = os.path.join(script_dir, "elements", "update_screen.PNG")
        found = self.invoke_rpa.element_exists(image_path, 30)
        if found:
            update_now_button = os.path.join(script_dir, "elements", "update_now_button.PNG")
            self.invoke_rpa.click(update_now_button)
            raise UpdateReynoldsException("Reynolds needs to update", 400)
        
    def login(self, user, password):
        script_dir = os.path.dirname(os.path.abspath(__file__))

        self.invoke_rpa.send_keyboard(user)
        self.invoke_rpa.send_keyboard("[tab]")
        self.invoke_rpa.send_keyboard(password)
        self.invoke_rpa.send_keyboard("[f12]")
        image_path = os.path.join(script_dir, "elements", "home_page.PNG")
        found = self.invoke_rpa.element_exists(image_path, 30)
        if found:
            self.logger.log("INFO", "ERA-IGNITE logged in successfully.")
        else:
            self.logger.log("INFO", "Error logging into ERA-IGNITE.")
            raise Exception("ERROR WHEN LOGGING INTO REYNOLDS")

    def reset_password(self, user, password, new_password):
        script_dir = os.path.dirname(os.path.abspath(__file__))

        self.invoke_rpa.send_keyboard(user)
        self.invoke_rpa.send_keyboard("[tab]")
        self.invoke_rpa.send_keyboard(password)
        self.invoke_rpa.send_keyboard("[tab]")
        image_path = os.path.join(script_dir, "elements", "home_page.PNG")
        time.sleep(0.5)
        
        image_path = os.path.join(script_dir, "elements", "change_pass_button.PNG")
        self.invoke_rpa.hover(image_path)
        mo_x, mo_y = self.invoke_rpa.return_coords()
        self.invoke_rpa.click_coords(mo_x, mo_y)
        
        image_path = os.path.join(script_dir, "elements", "change_password_screen.PNG")
        found = self.invoke_rpa.element_exists(image_path, 30)
        if found:
            self.logger.log("INFO", "Change password screen")
        else:
            self.logger.log("ERROR", "NOT Change password screen")
            raise Exception("ERROR CHANGING PASSWORD")
        
        self.invoke_rpa.send_keyboard(new_password)
        time.sleep(0.2)
        self.invoke_rpa.send_keyboard("[tab]")
        time.sleep(0.2)
        self.invoke_rpa.send_keyboard(new_password)
        time.sleep(0.2)
        self.invoke_rpa.send_keyboard("[tab]")
        time.sleep(0.2)
        self.invoke_rpa.send_keyboard("[enter]")

    def close_reynolds(self):
        print("TERMINATING REYNOLDS!!!")
        self.logger.log("INFO", f"TERMINATING REYNOLDS!!!")
        subprocess.run(["taskkill", "/F", "/IM", "PwrSuite.exe"], shell=True)
        time.sleep(3)

    def go_to_accounting(self, store, is_new_car=True):
        script_dir = os.path.dirname(os.path.abspath(__file__))

        if is_new_car:
            store_code = self.get_store_code_by_brand(store)
        else:
            store_code = self.get_store_code_by_store(store)


        self.logger.log("INFO", f"Navigating to store accounting for {store}")
        try:
            self.invoke_rpa.focus("(Home Page) - ERA-IGNITE - 01/01")
        except:
            try:
                self.invoke_rpa.focus("(ACC)")
            except:
                self.logger.log("ERROR", f"Error going to store accounting for {store}")
                raise Exception("ERROR WHEN GOING TO STORE ACCOUNTING")

        self.invoke_rpa.send_keyboard("[alt][end]")

        image_path = os.path.join(script_dir, "elements", "application_navigator_application_drop.PNG")
        application_navigator = self.invoke_rpa.element_exists(image_path, 15)
        if application_navigator == False:
            self.logger.log("ERROR", f"Couldnt locate application navigator {store}")
            raise Exception("COULDNT LOCALTE APPLICATION NAVIGATOR")
        
        image_path = os.path.join(script_dir, "elements", "application_navigator_application_drop.PNG")
        self.invoke_rpa.click(image_path)
        self.invoke_rpa.send_keyboard("Accounting")
        self.invoke_rpa.send_keyboard("[enter]")

        
        image_path = os.path.join(script_dir, "elements", "store_accounting.PNG")
        self.invoke_rpa.hover(image_path)
        mo_x, mo_y = self.invoke_rpa.return_coords()
        self.invoke_rpa.click_coords(mo_x + 50, mo_y)
        time.sleep(1)
        
        self.invoke_rpa.send_keyboard(store_code)
        time.sleep(1)

        self.invoke_rpa.send_keyboard("[f12]")

        accounting_main_page = os.path.join(script_dir, "elements", "accounting_main_page.PNG")
        found =  self.invoke_rpa.element_exists(accounting_main_page, 15)
        if found:
            self.logger.log("INFO", f"In store accounting for {store}")
        else:
            self.logger.log("ERROR", f"Error going to store accounting for {store}")
            raise Exception("ERROR WHEN ACCESSING TO THE STORE ACCOUNTING")
        
        
        time.sleep(5)        
        self.logger.log("INFO", f"Open the New Vehicle screen")
        self.invoke_rpa.send_keyboard("[alt]u")
        time.sleep(2)
        self.invoke_rpa.send_keyboard("v")
        time.sleep(2)
        self.invoke_rpa.send_keyboard("[enter]")

    def go_to_vms(self, store):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        store_code = self.get_store_code(store)

        self.logger.log("INFO", f"Navigating to store accounting for {store}")
        try:
            self.invoke_rpa.focus("(Home Page) - ERA-IGNITE - 01/01")
        except:
            try:
                self.invoke_rpa.focus("(ACC)")
            except:
                self.logger.log("ERROR", f"Error going to store accounting for {store}")
                raise Exception("ERROR WHEN GOING TO STORE ACCOUNTING")

        self.invoke_rpa.send_keyboard("[alt][end]")

        image_path = os.path.join(script_dir, "elements", "application_navigator_application_drop.PNG")
        application_navigator = self.invoke_rpa.element_exists(image_path, 15)
        if application_navigator == False:
            self.logger.log("ERROR", f"Couldnt locate application navigator {store}")
            raise Exception("COULDNT LOCALTE APPLICATION NAVIGATOR")
        
        image_path = os.path.join(script_dir, "elements", "application_navigator_application_drop.PNG")
        self.invoke_rpa.click(image_path)
        self.invoke_rpa.send_keyboard("Vehicle Management System")
        self.invoke_rpa.send_keyboard("[enter]")

        
        image_path = os.path.join(script_dir, "elements", "store_accounting.PNG")
        self.invoke_rpa.hover(image_path)
        mo_x, mo_y = self.invoke_rpa.return_coords()
        self.invoke_rpa.click_coords(mo_x + 50, mo_y)
        time.sleep(1)
        
        self.invoke_rpa.send_keyboard(store_code)
        time.sleep(1)

        self.invoke_rpa.send_keyboard("SALES01")

        self.invoke_rpa.send_keyboard("[f12]")

        vms_main_page = os.path.join(script_dir, "elements", "vms_main_page.PNG")
        found =  self.invoke_rpa.element_exists(vms_main_page, 15)
        if found:
            self.logger.log("INFO", f"In VMS for {store}")
        else:
            self.logger.log("ERROR", f"Error going to VMS for {store}")
            raise Exception("ERROR WHEN ACCESSING TO THE VMS")
        
    def select_new_or_used_car_to_stock_in(self, is_new_car = True):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        # Ensure inventory section is visible

        inventory_button = os.path.join(script_dir, "elements", "inventory_button.PNG")
        self.invoke_rpa.hover(inventory_button)

        if is_new_car == False:
            used_car_option = os.path.join(script_dir, "elements", "used_car_option.PNG")
            used_car_option_selected = os.path.join(script_dir, "elements", "used_car_option_selected.PNG")
            self.invoke_rpa.click(used_car_option)
            self.invoke_rpa.click(used_car_option_selected)
        else:
            new_car_option_selected = os.path.join(script_dir, "elements", "new_car_option_selected.PNG")
            new_car_option = os.path.join(script_dir, "elements", "new_car_option.PNG")
            self.invoke_rpa.click(new_car_option)
            self.invoke_rpa.click(new_car_option_selected)

        # Proceed to purchase information
        self.logger.log("INFO", f"Proceed to purchase information")
        self.invoke_rpa.send_keyboard("[f9]")

    def search_customer(self, transaction_location):
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # Look up to the customer find table
        self.invoke_rpa.send_keyboard("[f2]")

        search_customer_table = os.path.join(script_dir, "elements", "search_customer.PNG")
        found =  self.invoke_rpa.element_exists(search_customer_table, 15)
        if found:
            self.logger.log("INFO", f"Successfully accessed to the table to search customer")
        else:
            self.logger.log("ERROR", f"Error when accessed to the table to search customer")
            raise Exception("ERROR when accessed to the table to search customer")

        if transaction_location != "":
            self.invoke_rpa.send_keyboard(transaction_location)

        self.invoke_rpa.send_keyboard("[f5]")

        
        no_results_label = os.path.join(script_dir, "elements", "no_found_vendors.PNG")
        found =  self.invoke_rpa.element_exists(no_results_label, 10)
        if not found:
            self.logger.log("INFO", f"Successfully found customer")
            self.invoke_rpa.send_keyboard("[enter]")
        else:
            self.logger.log("ERROR", f"Error when looking for the customer")
            raise Exception("ERROR when looking for the customer")
        
    def save_other_amount_vals(self, vehicle):
        self.logger.log("INFO", f"Open Other amount screen")

        script_dir = os.path.dirname(os.path.abspath(__file__))

        time.sleep(2)

        image_path = os.path.join(script_dir, "elements", "new_other_accounts.PNG")
        self.invoke_rpa.hover(image_path)
        mo_x, mo_y = self.invoke_rpa.return_coords()
        self.invoke_rpa.click_coords(mo_x, mo_y + 20)

        dict_table_mapping = {}
        idx = 0
        while True:
            time.sleep(0.1)
            self.invoke_rpa.send_keyboard("[ctrl]c")
            time.sleep(0.1)
            acc_number = pyperclip.paste()

            time.sleep(0.1)
            self.invoke_rpa.send_keyboard("[right]")
            self.invoke_rpa.send_keyboard("[ctrl]c")
            self.invoke_rpa.send_keyboard("[left]")
            time.sleep(0.1)
            amount = pyperclip.paste()


            # If the current account matches the target, mark as found
            if amount == "" and acc_number != "":
                break

            dict_table_mapping[idx] = {"acc_number": acc_number, "amount": amount}

            # Move down to the next account in the list
            self.invoke_rpa.send_keyboard("[down]")

            idx += 1


        max_position = idx
        current_position = idx


        # Process MFR accounts (DT and CR) for MFR1 to MFR4
        other = False
        for i in range(1, 5):
            for field_suffix in ["AccountDT", "AccountCR"]:
                account_key = f"MFR{i}{field_suffix}"
                amount_key = f"MFR{i}Amount" + field_suffix[-2:]

                print(i , field_suffix)
                print(account_key)
                print(amount_key)

                # Only process if the account field is provided (not None)
                if vehicle.get(account_key):
                    self.logger.log("INFO", f"Fill {account_key}: {vehicle[account_key]} - {amount_key}: {vehicle[amount_key]}")

                    row_pos = 0
                    next_position = -1
                    for k, v in dict_table_mapping.items():
                        if vehicle[account_key] == v["acc_number"] and v["amount"] == "0.00":
                            next_position = row_pos
                            self.logger.log("INFO", f"Found at pos {row_pos}")
                        row_pos += 1

                    if next_position == -1:
                        next_position = max_position
                        max_position += 1

                    going_up = False
                    moving_pos = -1
                    if current_position > next_position:
                        going_up = True
                        moving_pos = current_position - next_position
                    else:
                        moving_pos = next_position - current_position

                    for move in range(moving_pos):
                        self.invoke_rpa.send_keyboard("[up]" if going_up else "[down]")

                    self.invoke_rpa.send_keyboard(vehicle[account_key])
                    time.sleep(1)
                    self.invoke_rpa.send_keyboard("[right]")
                    time.sleep(1)
                    self.invoke_rpa.send_keyboard(vehicle[amount_key] if "." in vehicle[amount_key] else f"{vehicle[amount_key]}.00")
                    time.sleep(1)
                    self.invoke_rpa.send_keyboard("[tab]")
                    time.sleep(1)
                    self.invoke_rpa.send_keyboard("[left]")
                    time.sleep(1)
                    self.invoke_rpa.send_keyboard("[left]")
                    time.sleep(1)

                    current_position = next_position

        # Save Other Amount
        self.invoke_rpa.send_keyboard("[f12]")

    def save_other_amount_vals_used_cars(self, vehicle):
        self.logger.log("INFO", f"Open Other amount screen")

        script_dir = os.path.dirname(os.path.abspath(__file__))

        time.sleep(2)

        image_path = os.path.join(script_dir, "elements", "new_other_accounts.PNG")
        self.invoke_rpa.hover(image_path)
        mo_x, mo_y = self.invoke_rpa.return_coords()
        self.invoke_rpa.click_coords(mo_x, mo_y + 20)

        dict_table_mapping = {}
        idx = 0
        while True:
            time.sleep(0.1)
            self.invoke_rpa.send_keyboard("[ctrl]c")
            time.sleep(0.1)
            acc_number = pyperclip.paste()

            time.sleep(0.1)
            self.invoke_rpa.send_keyboard("[right]")
            self.invoke_rpa.send_keyboard("[ctrl]c")
            self.invoke_rpa.send_keyboard("[left]")
            time.sleep(0.1)
            amount = pyperclip.paste()


            # If the current account matches the target, mark as found
            if amount == "" and acc_number != "":
                break

            dict_table_mapping[idx] = {"acc_number": acc_number, "amount": amount}

            # Move down to the next account in the list
            self.invoke_rpa.send_keyboard("[down]")

            idx += 1

        for i in range(idx):
            self.invoke_rpa.send_keyboard("[up]")

        self.invoke_rpa.send_keyboard("[right]")
        self.invoke_rpa.send_keyboard("[right]")
        self.invoke_rpa.send_keyboard("[right]")

        idx = 0
        while True:

            time.sleep(0.1)
            self.invoke_rpa.send_keyboard("[ctrl]c")
            time.sleep(0.1)
            description = pyperclip.paste()

            time.sleep(0.1)
            self.invoke_rpa.send_keyboard("[left]")
            self.invoke_rpa.send_keyboard("[ctrl]c")
            self.invoke_rpa.send_keyboard("[right]")
            time.sleep(0.1)
            val = pyperclip.paste()


            # If the current account matches the target, mark as found
            if description == "" and val == "":
                break

            dict_table_mapping[idx].update({"description": description})

            # Move down to the next account in the list
            self.invoke_rpa.send_keyboard("[down]")
            idx += 1
        
        for i in range(idx):
            self.invoke_rpa.send_keyboard("[up]")

        self.invoke_rpa.send_keyboard("[left]")
        self.invoke_rpa.send_keyboard("[left]")
        self.invoke_rpa.send_keyboard("[left]")

         # Process MFR accounts (DT and CR) for MFR1 to MFR4
        list_keys = [a.split("Amount")[0] for a in list(vehicle.keys()) if "AmountCR" in a or "AmountDT" in a]
        list_keys = list(set(list_keys))

        max_position = idx
        current_position = 0

        self.logger.log("INFO", f"{max_position}, {current_position}")

        for key in list_keys:
            for field_suffix in ["AccountDT", "AccountCR"]:
                account_key = f"{key}{field_suffix}"
                amount_key = f"{key}Amount" + field_suffix[-2:]

                self.logger.log("INFO", f"Looking {account_key}, {key}")


                row_pos = 0
                next_position = -1
                for k, v in dict_table_mapping.items():
                    if vehicle[account_key] == v["acc_number"] and key.lower() in v["description"].lower():
                        next_position = row_pos
                        self.logger.log("INFO", f"Found at pos {row_pos}")
                        break
                    row_pos += 1

                if next_position == -1:
                    next_position = max_position
                    max_position += 1


                going_up = False
                moving_pos = -1
                if current_position > next_position:
                    going_up = True
                    moving_pos = current_position - next_position
                else:
                    moving_pos = next_position - current_position

                for i in range(moving_pos):
                    self.invoke_rpa.send_keyboard("[up]" if going_up else "[down]")
                
                
                self.invoke_rpa.send_keyboard(vehicle[account_key])
                time.sleep(1)
                self.invoke_rpa.send_keyboard("[right]")
                time.sleep(1)
                self.invoke_rpa.send_keyboard(vehicle[amount_key] if "." in vehicle[amount_key] else f"{vehicle[amount_key]}.00")
                time.sleep(1)
                self.invoke_rpa.send_keyboard("[tab]")
                time.sleep(1)
                self.invoke_rpa.send_keyboard("[left]")
                time.sleep(1)
                self.invoke_rpa.send_keyboard("[left]")
                time.sleep(1)

                current_position = next_position
  
        self.invoke_rpa.send_keyboard("[f12]")

    def get_formatted_date_for_new_cars(self, date, store):
        # Date
        if ("pnw" in store.lower() or "por" in store.lower()) and date is None:
            date = str(datetime.now().strftime("%m/%d/%y"))
        try:
            date_obj = datetime.strptime(date, "%m/%d/%y")  # Primero intenta con MM/DD/YY
        except ValueError:
            try:
                date_obj = datetime.strptime(date, "%m/%d/%Y")  # Luego intenta con MM/DD/YYYY
            except ValueError:
                date_obj = datetime.strptime(date, "%Y-%m-%d")  # Finalmente intenta con YYYY-MM-DD

        formatted_date = date_obj.strftime("%m/%d/%y")
        return formatted_date
    
    def get_formatted_date_for_used_cars(self, date):
        # Date
        try:
            date_obj = datetime.strptime(date, "%m/%d/%y")  # Primero intenta con MM/DD/YY
        except ValueError:
            try:
                date_obj = datetime.strptime(date, "%m/%d/%Y")  # Luego intenta con MM/DD/YYYY
            except ValueError:
                try:
                    date_obj = datetime.strptime(date, "%Y-%m-%d")  # Finalmente intenta con YYYY-MM-DD
                except ValueError:
                    date_obj = datetime.strptime(date, "%Y-%m-%d %H:%M:%S")

        formatted_date = date_obj.strftime("%m/%d/%y")

        return formatted_date
    
    def fill_vehicle_purchases_transaction(self, vehicle, is_new_car = True):
        self.logger.log("INFO", f"Creating used vehicle: {str(vehicle)}")
        script_dir = os.path.dirname(os.path.abspath(__file__))

        try:

            image_path = os.path.join(script_dir, "elements", "purchase_information_box.PNG")
            found =  self.invoke_rpa.element_exists(image_path, 15)
            if found:
                self.logger.log("INFO", f"In stock in window")
            else:
                self.logger.log("ERROR", f"Not in stock in window")
                raise Exception("NOT IN STOCK IN WINDOW")
            
            # Prefix
            self.logger.log("INFO", f"Fill Prefix value: {vehicle['Prefix']}")
            image_path = os.path.join(script_dir, "elements", "new_prefix.PNG")
            found =  self.invoke_rpa.element_exists(image_path, 5)
            if found:
                self.invoke_rpa.click(image_path)
                self.invoke_rpa.send_keyboard(vehicle["Prefix"])
                self.invoke_rpa.send_keyboard("[enter]")
            else:
                self.invoke_rpa.send_keyboard("[tab]")


            # Reference number
            self.logger.log("INFO", f"Fill Reference number: {vehicle['ReferenceNumber']}")
            self.gui_utilities.set_clipboard(vehicle["ReferenceNumber"])
            self.gui_utilities.paste_clipboard()
            self.invoke_rpa.send_keyboard("[tab]")

            image_path = os.path.join(script_dir, "elements", "unposted_vehicle.PNG")
            found = self.invoke_rpa.element_exists(image_path, 15)
            if found:
                self.logger.log("INFO", f"Unposted vehicle")
            else:
                image_path = os.path.join(script_dir, "elements", "posted_status.PNG")
                found = self.invoke_rpa.element_exists(image_path, 15)
                if found:
                    self.logger.log("INFO", f"Posted vehicle")
                    raise PostedVehicleException("Vehicle already posted", 400)
                else:
                    image_path = os.path.join(script_dir, "elements", "reversed_status.PNG")
                    found = self.invoke_rpa.element_exists(image_path, 15)
                    if found:
                        self.logger.log("INFO", f"Reversed vehicle!")
                        raise PostedVehicleException("Vehicle already posted, it was reversed", 400)


            formatted_date = self.get_formatted_date_for_new_cars(vehicle["Date"], vehicle["Store"]) if is_new_car else self.get_formatted_date_for_used_cars(vehicle["Date"])

            self.logger.log("INFO", f"Fill Date: {formatted_date}")
            self.invoke_rpa.send_keyboard(formatted_date)

            posting_previous_month = os.path.join(script_dir, "elements", "posting_previous_month.PNG")
            exists_posting_previous_month = self.invoke_rpa.element_exists(posting_previous_month, 5)
            if exists_posting_previous_month:
                self.invoke_rpa.send_keyboard('[enter]')

        except PostedVehicleException as e:
            self.invoke_rpa.send_keyboard("[esc]")

            image_path = os.path.join(script_dir, "elements", "dont_save_anything_screen.PNG")
            dont_save_anything_screen = self.invoke_rpa.element_exists(image_path, 3)
            if dont_save_anything_screen:
                self.invoke_rpa.send_keyboard("[left]")
                time.sleep(0.5)
                self.invoke_rpa.send_keyboard("[enter]")
                time.sleep(0.5)

            raise PostedVehicleException(e.args[0], e.code, e.screenshot)

    def fill_vehicle_information(self, stock):
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # Stock number
        self.logger.log("INFO", f"Fill Stock number: {stock}")
        self.gui_utilities.set_clipboard(stock)
        self.gui_utilities.paste_clipboard()
        #self.invoke_rpa.send_keyboard(stock)
        self.invoke_rpa.send_keyboard("[tab]")

        self.logger.log("INFO", f"Checking if VIN field is filled")
        image_path = os.path.join(script_dir, "elements", "vin_field.PNG")
        self.invoke_rpa.hover(image_path)
        mo_x, mo_y = self.invoke_rpa.return_coords()
        self.invoke_rpa.click_coords(mo_x + 80, mo_y)
        vin_val = self.invoke_rpa.get_text_without_ctrl_a()
        self.logger.log("INFO", f"Value in vin field: {vin_val}")
        if vin_val is None or vin_val.replace(" ", "") == "":

            self.invoke_rpa.send_keyboard("[esc]")

            image_path = os.path.join(script_dir, "elements", "dont_save_anything_screen.PNG")
            dont_save_anything_screen = self.invoke_rpa.element_exists(image_path, 3)
            if dont_save_anything_screen:
                self.invoke_rpa.send_keyboard("[left]")
                time.sleep(0.5)
                self.invoke_rpa.send_keyboard("[enter]")
                time.sleep(0.5)

            raise VinDataException("Data not completed", 400)

    def fill_purchase_information(self, vehicle, is_new_car = True):
        script_dir = os.path.dirname(os.path.abspath(__file__))

        formatted_date = self.get_formatted_date_for_new_cars(vehicle["Date"], vehicle["Store"]) if is_new_car else self.get_formatted_date_for_used_cars(vehicle["Date"])
        # Received Date
        self.logger.log("INFO", f"Fill Received Date: {formatted_date}")
        image_path = os.path.join(script_dir, "elements", "new_received_date_label.PNG")
        self.invoke_rpa.hover(image_path)
        mo_x, mo_y = self.invoke_rpa.return_coords()
        self.invoke_rpa.click_coords(mo_x + 57, mo_y)
        self.invoke_rpa.send_keyboard(formatted_date)

        if is_new_car:
            # Vendor no
            self.logger.log("INFO", f"Fill Vendor number: {vehicle['VendorNumber']}")
            self.invoke_rpa.click_coords(mo_x + 57, mo_y + 41)
            self.invoke_rpa.send_keyboard(vehicle["VendorNumber"])
            self.invoke_rpa.send_keyboard("[tab]")
        else:
            # Change to CUSTOMER
            self.invoke_rpa.send_keyboard("C")
            time.sleep(0.5)
            self.invoke_rpa.send_keyboard("[tab]")
            time.sleep(0.1)

            #self.search_customer(vehicle['Company'], vehicle['Address'], vehicle['City'])
            self.search_customer(vehicle['Transaction location'])

    def fill_posting_details(self, vehicle, is_new_car = True):
        script_dir = os.path.dirname(os.path.abspath(__file__))

        if is_new_car:

            image_path = os.path.join(script_dir, "elements", "new_received_date_label.PNG")
            self.invoke_rpa.hover(image_path)
            mo_x, mo_y = self.invoke_rpa.return_coords()
            self.invoke_rpa.click_coords(mo_x + 57, mo_y)

            # Invoice amount
            self.logger.log("INFO", f"Fill Invoice amount: {vehicle['InvoiceAmount']}")
            self.invoke_rpa.click_coords(mo_x + 143, mo_y + 190)
            self.invoke_rpa.send_keyboard(vehicle["InvoiceAmount"])
            self.invoke_rpa.send_keyboard("[tab]")

            #Holdback amount
            if vehicle["HoldbackAmount"]: #it can be empty
                self.logger.log("INFO", f"Fill Holdback amount: {vehicle['HoldbackAmount']}")
                self.invoke_rpa.click_coords(mo_x + 143, mo_y + 209)
                self.invoke_rpa.send_keyboard(vehicle["HoldbackAmount"])
            
            self.invoke_rpa.send_keyboard("[tab]")

            if vehicle.get("PdiAllowance", None): #it can be empty
                self.logger.log("INFO", f"Fill PdiAllowance amount: {vehicle['PdiAllowance']}")
                self.invoke_rpa.send_keyboard(vehicle["PdiAllowance"])
            
            self.invoke_rpa.send_keyboard("[tab]")

            # Inventory amount
            self.logger.log("INFO", f"Fill Inventory amount: {vehicle['InventoryAmount']}")
            self.invoke_rpa.click_coords(mo_x + 143, mo_y + 248)
            self.invoke_rpa.send_keyboard(vehicle["InventoryAmount"])
            self.invoke_rpa.send_keyboard("[tab]")

            # Other Amount screen
            if vehicle['MFR1AccountDT'] or vehicle['MFR1AccountCR']:
                # image_path = os.path.join(script_dir, "elements", "new_received_date_label.PNG")
                # self.invoke_rpa.hover(image_path)
                mo_x, mo_y = self.invoke_rpa.return_coords()
                self.invoke_rpa.click_coords(mo_x, mo_y + 16)

                self.save_other_amount_vals(vehicle=vehicle)
                time.sleep(2)

            # VMS amount
            if vehicle["VMSEntry"]:  # it can be empty
                self.logger.log("INFO", f"Fill VMSEntry amount: {vehicle['VMSEntry']}")
                self.invoke_rpa.send_keyboard("[f10]")
                time.sleep(2)
                    
                image_path = os.path.join(script_dir, "elements", f"{vehicle['Brand'].lower()}_vms_label.PNG")
                self.invoke_rpa.hover(image_path)
                mo_x, mo_y = self.invoke_rpa.return_coords()
                self.invoke_rpa.click_coords(mo_x + 200, mo_y)
                self.invoke_rpa.send_keyboard(vehicle["VMSEntry"])
                self.invoke_rpa.send_keyboard("[tab]")
                self.invoke_rpa.send_keyboard("[f12]")
                time.sleep(1)
            
        else:

            inventory_amt_image = os.path.join(script_dir, "elements", "posting_details_table.PNG")
            self.invoke_rpa.hover(inventory_amt_image)
            mo_x, mo_y = self.invoke_rpa.return_coords()
            self.invoke_rpa.click_coords(mo_x + 10, mo_y)
            self.invoke_rpa.send_keyboard(vehicle['Inventory'])
            self.invoke_rpa.send_keyboard("[tab]")
            
            time.sleep(0.5)
            if vehicle['Payoff'] != "":
                self.invoke_rpa.send_keyboard(vehicle['Payoff'])
            self.invoke_rpa.send_keyboard("[tab]")

            time.sleep(0.5)
            self.invoke_rpa.send_keyboard(vehicle['Payable'])
            self.invoke_rpa.send_keyboard("[tab]")

            mo_x, mo_y = self.invoke_rpa.return_coords()
            self.invoke_rpa.click_coords(mo_x + 65, mo_y + 50)
            self.save_other_amount_vals_used_cars(vehicle)
            time.sleep(2)



        image_path = os.path.join(script_dir, "elements", "correct_balance_used_car.PNG")
        self.invoke_rpa.hover(image_path)
        time.sleep(0.5)
        self.invoke_rpa.send_keyboard("[f12]")
        time.sleep(2)

        if "hon" not in vehicle['Store'].lower():
            image_path = os.path.join(script_dir, "elements", "add_new_car_window.PNG")
            self.invoke_rpa.hover(image_path)
            time.sleep(1.5)
            self.invoke_rpa.send_keyboard("[f12]")
            time.sleep(1.5)
            self.invoke_rpa.send_keyboard("[enter]")
        
            posting_previous_month = os.path.join(script_dir, "elements", "posting_previous_month.PNG")
            exists_posting_previous_month = self.invoke_rpa.element_exists(posting_previous_month, 5)
            if exists_posting_previous_month:
                self.invoke_rpa.send_keyboard('[enter]')

        image_path = os.path.join(script_dir, "elements", "completely_finished.PNG")
        found =  self.invoke_rpa.element_exists(image_path, 15)
        if found:
            self.logger.log("INFO", f"Successfully saved")
        else:
            self.logger.log("ERROR", f"Not successfully saved")
            raise Exception("NOT SUCCESSFULLY SAVED")
            
    def insert_vehicle(self, vehicle, is_new_car = True):

        try:

            self.fill_vehicle_purchases_transaction(vehicle, is_new_car)
            self.fill_vehicle_information(vehicle["ReferenceNumber"])
            self.fill_purchase_information(vehicle, is_new_car)
            self.fill_posting_details(vehicle, is_new_car)

        except PostedVehicleException as e:
            raise PostedVehicleException(e.args[0], e.code, e.screenshot)
        
        except VinDataException as e:
            raise VinDataException(e.args[0], e.code, e.screenshot)
        
        except Exception:

            is_popup_exception = False
            script_dir = os.path.dirname(os.path.abspath(__file__))
            image_path = os.path.join(script_dir, "elements", "error_signal.PNG")
            error_signal = self.invoke_rpa.element_exists(image_path, 3)
            pop_up_exception = None
            if error_signal:
                pop_up_exception = PopUpException("Pop up error", 400)
                self.invoke_rpa.send_keyboard("[enter]")
                is_popup_exception = True
                time.sleep(1)
            
            self.invoke_rpa.send_keyboard("[esc]")
            time.sleep(1)

            image_path = os.path.join(script_dir, "elements", "dont_save_anything_screen.PNG")
            dont_save_anything_screen = self.invoke_rpa.element_exists(image_path, 3)
            if dont_save_anything_screen:
                self.invoke_rpa.send_keyboard("[left]")
                time.sleep(0.5)
                self.invoke_rpa.send_keyboard("[enter]")
                time.sleep(0.5)

            if is_popup_exception:
                raise pop_up_exception
            else:
                raise Exception("EXCEPTION HAS OCURR")

    def get_store_code_by_brand(self, store_name):
        """
        Return a store code based on the given store_name.
        If the store_name is not in the dictionary, return None (or raise an error).
        """
        # Dictionary mapping store names to codes
        store_mapping = {
            "LEXUS": "01",
            "PORSCHE": "02",
            "HONDA": "03",
            "FORD": "04",
            "LINCOLN": "04",
            "CADILLAC": "06",
            "MBG": "07",
            "LANDROVER": "08",
            "JAGUAR": "08",
            "MAZDA": "07"
        }

        # Convert input to uppercase and strip spaces, just to be safe
        normalized_name = store_name.upper().strip()

        # Return the matching code or None if not found
        return store_mapping.get(normalized_name)
    
    def get_store_code_by_store(self, store_name):
        """
        Return a store code based on the given store_name.
        If the store_name is not in the dictionary, return None (or raise an error).
        """
        # Dictionary mapping store names to codes
        store_mapping = {
            "LOA": "01",
            "LOG": "01",
            "PNW": "02",
            "POR": "02",
            "HON": "03",
            "FOR": "04",
            "CAD": "06",
            "MBG": "07",
            "JLRG": "08",
            "JLRN": "08",
            "JLRB": "08",
        }

        # Convert input to uppercase and strip spaces, just to be safe
        normalized_name = store_name.upper().strip()

        # Return the matching code or None if not found
        return store_mapping.get(normalized_name)