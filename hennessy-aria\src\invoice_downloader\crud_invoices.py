# This module contains a class for interacting with the Emails collection in the MongoDB database.

import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudInvoices:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='invoice'
        )
    
    def insert_invoice(self, vin, file_path, stage):
        """
        This function inserts a folio into the database.
        """

        invoice_document = {
            "vin": vin,
            "file_path": file_path,  
            "read_at": datetime.datetime.now(),
            "current_flow": stage
        }

        self.mongo.insert_one(invoice_document)


    def find_invoice_vin(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin}
        return self.mongo.find_one(query)
    
    def find_invoice_vin_and_stage(self, vin, stage):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin, "current_flow": stage}
        return self.mongo.find_one(query)


    def __del__(self):
        self.mongo.close_connection()
