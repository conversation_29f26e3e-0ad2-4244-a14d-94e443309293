from utils.crud_bre_matrix import CrudBreMatrix
from utils.crud_vins import CrudVins
from datetime import datetime
from utils.boto3_utils import get_secret
import os
import pymongo

VALID_RULES = {
    "invoice": {
        'lexus': {
            'required_fields_and_calculations': [41, 45, 46, 88, 48, 91, 85, 86, 90, 52, 53, 54, 56, 57, 67, 68, 69, 70, 71, 72, 73, 74, 84, 92, 76, 81, 83, 80, 82, 87, 95],
            'submit': [41, 45, 46, 88, 48, 91, 85, 86, 52, 53, 54, 56, 57, 67, 68, 69, 70, 71, 72, 73, 74, 84, 92, 76, 80, 81, 82, 83, 87, 95],
            'on_hold': [41, 45, 46, 88, 48, 91, 85, 86, 52, 53, 54, 56, 57, 67, 68, 69, 70, 71, 72, 73, 74, 84, 92, 76, 80, 81, 82, 83, 87, 95],
            'save': [],
            'review_hold': [],
            'inspection': [],
            'manually': [],
            'completed': [],
            'ready_again': []
        },
        'porsche':{
            'required_fields_and_calculations': [41, 45, 46, 48, 49, 51, 91, 85, 86, 89, 90, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 84, 81, 83, 80, 82, 87, 93],
            'submit': [41, 45, 46, 48, 49, 51, 91, 85, 86, 89, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 84, 80, 81, 82, 83, 87, 93],
            'on_hold': [41, 45, 46, 48, 49, 51, 91, 85, 86, 89, 52, 53, 54, 67, 68, 69, 70, 71, 72, 73, 74, 84, 80, 81, 82, 83, 87, 93],
            'save': [],
            'review_hold': [],
            'inspection': [],
            'manually': [],
            'completed': [],
            'ready_again': []
        },
        'honda': {
            'required_fields_and_calculations': [41, 42, 45, 46, 47, 48, 91, 85, 86, 90, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 84, 76, 77, 78, 79, 81, 83, 80, 82, 87, 96],
            'submit': [41, 42, 45, 46, 47, 48, 91, 85, 86, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 84, 76, 77, 78, 79, 80, 81, 82, 83, 87, 96],
            'on_hold': [41, 42, 45, 46, 47, 48, 91, 85, 86, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 84, 76, 77, 78, 79, 80, 81, 82, 83, 87, 96],
            'save': [],
            'review_hold': [],
            'inspection': [],
            'manually': [],
            'completed': [],
            'ready_again': []
        },
        'ford': {
            'required_fields_and_calculations': [41, 45, 46, 47, 48, 50, 91, 85, 86, 90, 52, 53, 54, 55, 56, 57, 60, 68, 69, 70, 71, 72, 73, 74, 84, 75, 76, 77, 81, 83, 80, 82, 87],
            'submit': [41, 45, 46, 47, 48, 50, 91, 85, 86, 52, 53, 54, 55, 56, 57, 60, 68, 69, 70, 71, 72, 73, 74, 84, 75, 76, 77, 80, 81, 82, 83, 87],
            'on_hold': [41, 45, 46, 47, 48, 91, 50, 85, 86, 52, 53, 54, 55, 56, 57, 60, 68, 69, 70, 71, 72, 73, 74, 84, 75, 76, 77, 80, 81, 82, 83, 87],
            'save': [],
            'review_hold': [],
            'inspection': [],
            'manually': [],
            'completed': [],
            'ready_again': []
        },
        'lincoln': {
            'required_fields_and_calculations': [41, 45, 46, 48, 50, 91, 85, 86, 90, 52, 53, 54, 55, 56, 57, 76, 68, 69, 70, 71, 72, 73, 74, 84, 81, 83, 80, 82, 87],
            'submit': [41, 45, 46, 48, 50, 91, 85, 86, 52, 53, 54, 55, 56, 57, 76, 68, 69, 70, 71, 72, 73, 74, 84, 80, 81, 82, 83, 87],
            'on_hold': [41, 45, 46, 48, 50, 91, 85, 86, 52, 53, 54, 55, 56, 57, 76, 68, 69, 70, 71, 72, 73, 74, 84, 80, 81, 82, 83, 87],
            'save': [],
            'review_hold': [],
            'inspection': [],
            'manually': [],
            'completed': [],
            'ready_again': []
        },
        'cadillac': {
            'required_fields_and_calculations': [41, 43, 46, 48, 91, 85, 86, 90, 52, 53, 54, 55, 56, 57, 68, 69, 70, 71, 72, 73, 74, 84, 76, 81, 80, 82, 87, 94],
            'submit': [41, 43, 46, 48, 91, 85, 86, 52, 53, 54, 55, 56, 57, 68, 69, 70, 71, 72, 73, 74, 84, 76, 80, 81, 82, 87, 94],
            'on_hold': [41, 43, 46, 48, 91, 85, 86, 52, 53, 54, 55, 56, 57, 68, 69, 70, 71, 72, 73, 74, 84, 76, 80, 81, 82, 87, 94],
            'save': [],
            'review_hold': [],
            'inspection': [],
            'manually': [],
            'completed': [],
            'ready_again': []
        },
        'gm': {
            'required_fields_and_calculations': [41, 45, 46, 47, 48, 91, 85, 86, 90, 52, 53, 54, 55, 56, 57, 58, 59, 60, 68, 69, 70, 71, 72, 73, 74, 84, 75, 76, 77, 81, 83, 80, 82, 87],
            'submit': [41, 45, 46, 47, 48, 91, 85, 86, 52, 53, 54, 55, 56, 57, 58, 59, 60, 68, 69, 70, 71, 72, 73, 74, 84, 75, 76, 77, 80, 81, 82, 83, 87],
            'on_hold': [41, 45, 46, 47, 48, 91, 85, 86, 52, 53, 54, 55, 56, 57, 58, 59, 60, 68, 69, 70, 71, 72, 73, 74, 84, 75, 76, 77, 80, 81, 82, 83, 87],
            'save': [],
            'review_hold': [],
            'inspection': [],
            'manually': [],
            'ready_again': []
        },
        'mazda': {
            'required_fields_and_calculations': [41, 42, 45, 46, 47, 48, 51, 91, 85, 86, 90, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 68, 69, 70, 71, 72, 73, 74, 84, 76, 77, 78, 81, 83, 80, 82, 87],
            'submit': [41, 42, 45, 46, 47, 48, 51, 91, 85, 86, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 68, 69, 70, 71, 72, 73, 74, 84, 76, 77, 78, 80, 81, 82, 83, 87],
            'on_hold': [41, 42, 45, 46, 47, 48, 51, 91, 85, 86, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 68, 69, 70, 71, 72, 73, 74, 84, 76, 77, 78, 80, 81, 82, 83, 87],
            'save': [],
            'review_hold': [],
            'inspection': [],
            'completed': [],
            'ready_again': []
        },
        'jaguar': {
            'required_fields_and_calculations': [41, 45, 46, 48, 91, 85, 86, 90, 52, 53, 54, 68, 69, 70, 71, 72, 73, 74, 84, 81, 83, 80, 82, 87],
            'submit': [41, 45, 46, 48, 91, 85, 86, 52, 53, 54, 68, 69, 70, 71, 72, 73, 74, 84, 80, 81, 82, 83, 87],
            'on_hold': [41, 45, 46, 48, 91, 85, 86, 52, 53, 54, 68, 69, 70, 71, 72, 73, 74, 84, 80, 81, 82, 83, 87],
            'save': [],
            'review_hold': [],
            'inspection': [],
            'manually': [],
            'completed': [],
            'ready_again': []
        },
        'landrover': {
            'required_fields_and_calculations': [41, 45, 46, 48, 91, 85, 86, 52, 90, 53, 54, 68, 69, 70, 71, 72, 73, 74, 84, 81, 83, 80, 82, 87],
            'submit': [41, 45, 46, 48, 91, 85, 86, 52, 53, 54, 68, 69, 70, 71, 72, 73, 74, 84, 80, 81, 82, 83, 87],
            'on_hold': [41, 45, 46, 48, 91, 85, 86, 52, 53, 54, 68, 69, 70, 71, 72, 73, 74, 84, 80, 81, 82, 83, 87],
            'save': [],
            'review_hold': [],
            'inspection': [],
            'manually': [],
            'completed': [],
            'ready_again': []
        }
    },
    
    'title': {
        'required_fields_and_calculations': [1],        
        'submit': [1]
    },

    'bol': {
        'required_fields_and_calculations': [22, 23, 24, 25],        
        'submit': [21, 22, 24, 26],
        'complete_manually': [21, 22, 26]
    }
}

def get_rules_titles(document_id, parsed_fields):
    rules_titles = {
        1: {
            'tag': 'vins',
            'output_tags': ['vins'],
            'pre_req': [],
            'arguments': [parsed_fields]
            },
    }

    return rules_titles

def get_rules_bols(document_id, parsed_fields, mongo_client):
    
    crud_vin = CrudVins(mongo_client)

    mongo_client.select_db_and_collection(os.environ['MONGO_DATABASE'], collection_name='bre_matrix')
    bre_matrix_rules = mongo_client.find({})
    
    address_of_supported_store = {}
    for rules in bre_matrix_rules:
        address_of_supported_store[rules['STORE']] = rules['ADDRESS']

    rules_bols = {
        21: {
            'tag': 'bol_date',
            'output_tags': ['bol_date'],
            'pre_req': [],
            'arguments': [parsed_fields['bol_date'].get('value') if "None" not in str(parsed_fields.get('bol_date', {}).get("value", None)) else '']
            },
        22: {
            'tag': 'bol_vins',
            'output_tags': ['bol_vins', 'vins_in_document'],
            'pre_req': [],
            'arguments': [parsed_fields, crud_vin] 
            },
        23: {
            'tag': 'bol_date',
            'output_tags': ['bol_date'],
            'pre_req': [],
            'arguments': [parsed_fields]
            },
        24: {
            'tag': 'bol_vins',
            'output_tags': ['bol_vins'],
            'pre_req': [],
            'arguments': [parsed_fields, crud_vin] 
            },
        25: {
            'tag': 'address',
            'output_tags': ['address', 'store'],
            'pre_req': [],
            'arguments': [parsed_fields, address_of_supported_store] 
            },
        26: {
            'tag': 'store',
            'output_tags': ['store'],
            'pre_req': [],
            'arguments': [str(parsed_fields['store'].get('value')) if parsed_fields.get('store', '') else ''] 
            }
    }

    return rules_bols


def get_rules_invoices(document_id, parsed_fields, mongo_client):
    vendor = parsed_fields.get("make").get("value").lower()
    
    rows_reynols = parsed_fields['reynols_report'].get("rows", {})
    store = None
    vin_report_value = None
    for k, v in rows_reynols.items():
        field_name = v['cells']['field']['value']
        if "store" in field_name.lower():
            store = v['cells']['value']['value']
        if "VIN" in field_name:
            vin_report_value = v['cells']['value']['value']

    mapping_fields_row_uuid = {}
    rows_stock_in = parsed_fields['stock_in_values'].get("rows", {})
    for k, v in rows_stock_in.items():
        field_name = v['cells']['field']['value']
        mapping_fields_row_uuid[field_name] = k

    crud_bre_matrix = CrudBreMatrix(mongo_client)

    store_brand_vals = crud_bre_matrix.get_bre_matrix_by_store_and_brand(store, vendor)
    
    crud_vin = CrudVins(mongo_client)
    mongo_vin_date = crud_vin.find_report_row_by_vin(vin_report_value)['read_at']
    days_passed = (datetime.today() - mongo_vin_date).days


    mongo_uri = get_secret(f"{os.environ['ENV']}-mongodb_uri", return_json = False)
    client = pymongo.MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
    db = client[os.environ['MONGO_DATABASE']]
    

    rules_invoices = {
        41: {
            'tag': 'make',
            'output_tags': ['make'],
            'pre_req': [],
            'arguments': [str(parsed_fields['make'].get('value')) if parsed_fields.get('make', '') else '']
            },
        42: {
            'tag': 'model_code',
            'output_tags': ['model_code'],
            'pre_req': [],
            'arguments': [str(parsed_fields['model_code'].get('value')) if parsed_fields.get('model_code', '') else '', vendor, db]
            },
        43: {
            'tag': 'model_description',
            'output_tags': ['model_description'],
            'pre_req': [],
            'arguments': [str(parsed_fields['model_description'].get('value')) if parsed_fields.get('model_description', '') else '']
            },
        44: {
            'tag': 'delivery_charge',
            'output_tags': ['delivery_charge'],
            'pre_req': [],
            'arguments': [str(parsed_fields['delivery_charge'].get('value')) if parsed_fields.get('delivery_charge', '') else '', vendor]
            },
        45: {
            'tag': 'sold_to',
            'output_tags': ['sold_to'],
            'pre_req': [],
            'arguments': [str(parsed_fields['sold_to'].get('value')) if parsed_fields.get('sold_to', '') else '']
            },
        46: {
            'tag': 'vin',
            'output_tags': ['vin'],
            'pre_req': [],
            'arguments': [str(parsed_fields['vin'].get('value')) if parsed_fields.get('vin', '') else '']
            },
        47: {
            'tag': 'hold_back',
            'output_tags': ['hold_back'],
            'pre_req': [],
            'arguments': [str(parsed_fields['hold_back'].get('value')) if parsed_fields.get('hold_back', '') else '', vendor]
            },
        48: {
            'tag': 'total_dealer_invoice',
            'output_tags': ['total_dealer_invoice'],
            'pre_req': [],
            'arguments': [str(parsed_fields['total_dealer_invoice'].get('value')) if parsed_fields.get('total_dealer_invoice', '') else '', vendor]
            },
        49: {
            'tag': 'domestic_wholesale',
            'output_tags': ['domestic_wholesale'],
            'pre_req': [],
            'arguments': [str(parsed_fields['domestic_wholesale'].get('value')) if parsed_fields.get('domestic_wholesale', '') else '', vendor]
            },
        50: {
            'tag': 'total_vehicle_&_options',
            'output_tags': ['total_vehicle_&_options'],
            'pre_req': [],
            'arguments': [str(parsed_fields['total_vehicle_&_options'].get('value')) if parsed_fields.get('total_vehicle_&_options', '') else '', vendor]
            },
        51: {
            'tag': 'retail_amount',
            'output_tags': ['retail_amount'],
            'pre_req': [],
            'arguments': [str(parsed_fields['retail_amount'].get('value')) if parsed_fields.get('retail_amount', '') else '', vendor]
            },
        52: {
            'tag': 'reynols_report',
            'output_tags': ['reynols_report'],
            'pre_req': [],
            'arguments': [parsed_fields]
            },
        53: { # INVOICE AMT
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [48],
            'arguments': [parsed_fields, mapping_fields_row_uuid]
            },
        54: { #INVENTORY AMT
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [48],
            'arguments': [parsed_fields, mapping_fields_row_uuid]
            },
        55: { #MFR 1 AMT DT
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            # 'pre_req': [48] if vendor == "lexus" else
            #             [48] if vendor == "honda" else
            #             [50] if vendor == "ford" else
            #             [48] if vendor == "cadillac" else
            #             [48] if vendor == "gm" else
            #             [47] if vendor == "mazda" else [],
            'pre_req': [43] if vendor == "cadillac" else 
                        [47] if vendor == "honda" else [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vendor, db]
            },
        56: { #MFR 1 ACCT1-DT
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        57: { #MFR 1 ACCT2-CR
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        58: { #MFR 2 AMT
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            # 'pre_req': [] if vendor == "honda" else
            #             [48] if vendor == "ford" else
            #             [48] if vendor == "gm" else
            #             [50] if vendor == "mazda" else [],
            'pre_req': [42, 96] if vendor == "honda" else [],
            'rules_results_values': ['color_upcharge_fee'] if vendor == "honda" else [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vendor, db]
            },
        59: { #MFR 2 ACCT1-DT
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        60: { #MFR 2 ACCT2-CR
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        61: { #MFR 3 AMT DT
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            # 'pre_req': [] if vendor == "honda" else
            #             [48] if vendor == "mazda" else [],
            'pre_req': [42, 96] if vendor == "honda" else [],
            'rules_results_values': ['color_upcharge_fee'] if vendor == "honda" else [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vendor, db]
            },
        62: { #MFR 3 ACCT1-DT
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        63: { #MFR 3 ACCT2-CR
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        64: { #MFR 4 AMT DT
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            #'pre_req': [] if vendor == "honda" else [],
            'pre_req': [42, 96] if vendor == "honda" else [],
            'rules_results_values': ['color_upcharge_fee'] if vendor == "honda" else [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vendor, db]
            },
        65: { #MFR 4 ACCT1-DT
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        66: { #MFR 4 ACCT2-CR
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        67: { #VMS ENTRY
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            # 'pre_req': [47] if vendor == "lexus" else
            #             [49] if vendor == "porsche" else
            #             [51] if vendor == "mazda" else [],
            'pre_req': [51] if vendor == "porsche" else [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store, vendor]
            },
        68: { #STORE
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        69: { #BRAND
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        70: { #PREFIX
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        71: { #REFERENCE
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [85],
            'arguments': [parsed_fields, mapping_fields_row_uuid]
            },
        72: { #DATE
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid]
            },
        73: { #VENDOR
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, store_brand_vals]
            },
        74: { #Invoice ro date
            'tag': 'ro_date',
            'output_tags': ['ro_date'],
            'pre_req': [46, -93] if vendor == "porsche" else [46],
            'arguments': [parsed_fields['ro_date'].get('value') if "None" not in str(parsed_fields.get('ro_date', {}).get("value", None)) else '',
                          parsed_fields['bol_date'].get('value') if "None" not in str(parsed_fields.get('bol_date', {}).get("value", None)) else '',
                          days_passed]
            },
        75: { #HOLDBACK AMT TABLE
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vendor]
            },
        76: { #MFR 1 AMT CR
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            # 'pre_req': [48] if vendor == "lexus" else
            #             [48] if vendor == "honda" else
            #             [50] if vendor == "ford" else
            #             [48] if vendor == "cadillac" else
            #             [48] if vendor == "gm" else
            #             [47] if vendor == "mazda" else [],
            'pre_req': [43] if vendor == "cadillac" else
                        [47] if vendor == "honda" else [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vendor, db]
            },
        77: { #MFR 2 AMT CR
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            # 'pre_req': [] if vendor == "honda" else
            #             [48] if vendor == "ford" else
            #             [48] if vendor == "gm" else
            #             [50] if vendor == "mazda" else [],
            'pre_req': [42, 96] if vendor == "honda" else [],
            'rules_results_values': ['color_upcharge_fee'] if vendor == "honda" else [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vendor, db]
            },
        78: { #MFR 3 AMT CR
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            # 'pre_req': [] if vendor == "honda" else
            #             [48] if vendor == "mazda" else [],
            'pre_req': [42, 96] if vendor == "honda" else [],
            'rules_results_values': ['color_upcharge_fee'] if vendor == "honda" else [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vendor, db]
            },
        79: { #MFR 4 AMT CR
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            #'pre_req': [] if vendor == "honda" else [],
            'pre_req': [42, 96] if vendor == "honda" else [],
            'rules_results_values': ['color_upcharge_fee'] if vendor == "honda" else [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vendor, db]
            },
        80: { #Invoice RO date NOT FILLED FOR 60 days
            'tag': 'ro_date',
            'output_tags': ['ro_date'],
            'pre_req': [46, -93] if vendor == "porsche" else [46],
            'arguments': [parsed_fields, days_passed]
            },
        81: { #Check vin extracted is same as the report
            'tag': 'vin',
            'output_tags': ['vin'],
            'pre_req': [46],
            'arguments': [parsed_fields, vin_report_value]
            },
        82: { #Invoice BOL date NOT FILLED FOR 60 days
            'tag': 'bol_date',
            'output_tags': ['bol_date'],
            'pre_req': [46, -93] if vendor == "porsche" else [46],
            'arguments': [parsed_fields, days_passed]
            },
        83: { # SOLD TO Validation
            'tag': 'sold_to',
            'output_tags': ['sold_to'],
            'pre_req': [45],
            'arguments': [parsed_fields, store_brand_vals]
            },
        84: { #Invoice bol date
            'tag': "bol_date",
            'output_tags': ["bol_date"],
            'pre_req': [46, -93] if vendor == "porsche" else [46],
            'arguments': [parsed_fields['bol_date'].get('value') if "None" not in str(parsed_fields.get('bol_date', {}).get("value", None))  else '',
                          parsed_fields['ro_date'].get('value') if "None" not in str(parsed_fields.get('ro_date', {}).get("value", None)) else '',
                          days_passed]
            },
        85: {
            'tag': 'stock_number',
            'output_tags': ['stock_number'],
            'pre_req': [],
            'arguments': [str(parsed_fields['stock_number'].get('value')) if parsed_fields.get('stock_number', '') else '']
            },
        86: {
            'tag': 'store',
            'output_tags': ['store'],
            'pre_req': [],
            'arguments': [str(parsed_fields['store'].get('value')) if parsed_fields.get('store', '') else '']
            },
        87: { # FILL VIN IN DMS DATA
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vin_report_value]
            },
        88: { # wholesale_finance_reserve
            'tag': 'wholesale_finance_reserve',
            'output_tags': ['wholesale_finance_reserve'],
            'pre_req': [],
            'arguments': [str(parsed_fields['wholesale_finance_reserve'].get('value')) if parsed_fields.get('wholesale_finance_reserve', '') else '']
            },
        89: {
            'tag': 'cocar',
            'output_tags': ['cocar'],
            'pre_req': [],
            'arguments': [str(parsed_fields['cocar'].get('value')) if parsed_fields.get('cocar', '') else '']
            },
        90: {
            'tag': 'electric_vehicle',
            'output_tags': ['electric_vehicle'],
            'pre_req': [],
            'arguments': [parsed_fields, store, vendor, vin_report_value]
            },
        91: {
            'tag': 'invoice_date',
            'output_tags': ['invoice_date'],
            'pre_req': [],
            'arguments': [parsed_fields['invoice_date'].get('value') if "None" not in str(parsed_fields.get('invoice_date', {}).get("value", None)) else '']
            },
        92: { #PDI ALLOWANCE AMT TABLE
            'tag': 'stock_in_values',
            'output_tags': ['stock_in_values'],
            'pre_req': [],
            'arguments': [parsed_fields, mapping_fields_row_uuid, vendor]
            },
        93: {
            'tag': 'porsche_experience_delivery',
            'output_tags': ['porsche_experience_delivery'],
            'pre_req': [],
            'arguments': [str(parsed_fields['porsche_experience_delivery'].get('value')) if parsed_fields.get('porsche_experience_delivery', '') else '']
            },
        94: {
            'tag': 'msrp_amount',
            'output_tags': ['msrp_amount'],
            'pre_req': [],
            'arguments': [str(parsed_fields['msrp_amount'].get('value')) if parsed_fields.get('msrp_amount', '') else '', vendor]
            },
        95: {
            'tag': 'lccs_car',
            'output_tags': ['lccs_car'],
            'pre_req': [],
            'arguments': [str(parsed_fields['lccs_car'].get('value')) if parsed_fields.get('lccs_car', '') else '']
            },
        96: {
            'tag': 'color_upcharge_fee',
            'output_tags': ['color_upcharge_fee'],
            'pre_req': [],
            'arguments': [str(parsed_fields['color_upcharge_fee'].get('value')) if parsed_fields.get('color_upcharge_fee', '') else '']
            }   
    }   

    return rules_invoices

def get_rules_json(document_type, document_id, parsed_fields, mongo_client):
    if "invoice" in document_type:
        return get_rules_invoices(document_id, parsed_fields, mongo_client)
    elif "bol" in document_type:
        return get_rules_bols(document_id, parsed_fields, mongo_client)
    elif "title" in document_type:
        return get_rules_titles(document_id, parsed_fields)
