from Utilities.Common_Utilities.logger_utility import Logger
import pypyodbc
import traceback

class Sql_server_db():

    def __init__(self,log_path):
        self.l = Logger(log_path)

    # Init sql server db
    #   INPUT:
    #       -server: name of server
    #       -db_name: name of db
    #   OUTPUT:
    #       -con: connection stablished
    def init_db(self, server, db_name):
        try:
            con = pypyodbc.connect('Driver={SQL Server};Server={'+ server +'};Database={'+ db_name +'};Trusted_Connection=True;')
            cursor = con.cursor()
            return cursor
        except Exception as e:            
            raise Exception ('Exception occurred on init_db method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    # Execute query
    #   INPUT:
    #       -sql_query: query to execute
    #       -con: connection with .db
    #   OUTPUT:
    #       -results: results of the DB in case a SELECT is executed
    def execute_query(self, sql_query, con):
        try:
            type_of_request = sql_query.split(" ")[0]
            match type_of_request:
                case "SELECT":
                    cursor = con.execute(sql_query)
                    return cursor.fetchall()
                case "INSERT" | "UPDATE" | "DELETE" | "DROP" | "TRUNCATE":
                    cursor = con.execute(sql_query)
                    con.commit()
                    return
                case _:
                    raise Exception ("Invalid query")
        except Exception as e:           
            raise Exception ('Exception occurred on execute_query method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))