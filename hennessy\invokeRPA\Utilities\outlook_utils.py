import base64
import requests
import urllib.parse
import msal

IDENTITY_ENDPOINT = 'https://login.microsoftonline.com/'
GRAPH_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/'
GET_EMAIL_FROM_INTERNET_MESSAGE_ID = (
    "https://graph.microsoft.com/v1.0/users/{user_id}/messages?"
    "$filter=internetMessageId eq '{email_internet_message_id}'"
)


def print_error_details(message, response):
    """
    Prints the details of an error response.
    """
    print(message)
    print('Status code:', response.status_code)
    try:
        print('Response content:', response.json())
    except Exception:
        print('Response content:', response.text)


class Outlook:
    """
    This class provides methods to interact with the Outlook API.
    It is initialized with the necessary credentials.
    """

    def __init__(self, client_id, client_secret, tenant_id, user_id):
        """
        Initializes the Outlook class with client credentials and configuration.

        Parameters:
            client_id (str): The client (application) ID.
            client_secret (str): The client secret.
            tenant_id (str): The tenant ID.
            user_id (str): The user ID (email address or GUID) for mailbox operations.
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.tenant_id = tenant_id
        self.user_id = user_id

    def get_access_token(self):
        """
        Retrieves an access token from Azure AD using the client credentials flow.

        Returns:
            str: The access token.

        Raises:
            Exception: If the token acquisition fails.
        """
        authority_url = f"{IDENTITY_ENDPOINT}{self.tenant_id}"
        scopes = ['https://graph.microsoft.com/.default']

        # Initialize the MSAL ConfidentialClientApplication with client credentials.
        app = msal.ConfidentialClientApplication(
            self.client_id,
            authority=authority_url,
            client_credential=self.client_secret
        )
        result = app.acquire_token_for_client(scopes)

        if 'access_token' in result:
            return result['access_token']
        else:
            error = result.get('error')
            error_desc = result.get('error_description')
            print('Failed to acquire token:', error, error_desc)
            raise Exception('Failed to acquire access token')

    def get_id_from_email(self, email_internet_message_id):
        """
        Retrieves the email ID based on its Internet message ID.

        Parameters:
            email_internet_message_id (str): The Internet message ID of the email.

        Returns:
            str or None: The internal email ID if found; otherwise, None.
        """
        # URL encode the email Internet message ID
        encoded_email_id = urllib.parse.quote(email_internet_message_id)
        endpoint = GET_EMAIL_FROM_INTERNET_MESSAGE_ID.format(
            user_id=self.user_id,
            email_internet_message_id=encoded_email_id
        )

        access_token = self.get_access_token()
        headers = {'Authorization': f'Bearer {access_token}'}
        response = requests.get(endpoint, headers=headers)

        if response.status_code == 200:
            emails = response.json().get('value', [])
            for email in emails:
                return email['id']
        else:
            print('Error fetching email ID:', response.json())

        return None

    def get_folder_id(self, folder_name, endpoint_path="/mailFolders"):
        """
        Fetches the ID of the specified folder.

        Parameters:
            folder_name (str): The name of the folder.
            endpoint_path (str): The relative API endpoint path (default is '/mailFolders').

        Returns:
            str or None: The folder ID if found; otherwise, None.
        """
        access_token = self.get_access_token()
        endpoint = f'{GRAPH_ENDPOINT}{self.user_id}{endpoint_path}?$top=30'
        headers = {'Authorization': f'Bearer {access_token}'}
        r = requests.get(endpoint, headers=headers)
        if r.ok:
            data = r.json()
            for folder in data.get('value', []):
                if folder.get('displayName', '').lower() == folder_name.lower():
                    return folder['id']

            # If not found and we are looking at the root folders, try searching in the inbox.
            if endpoint_path == "/mailFolders":
                return self.get_folder_id(folder_name, "/mailFolders/inbox")
            else:
                return None
        else:
            print_error_details('Failed to fetch folders', r)
            return None

    def get_emails_from_folder(self, folder_name, top_fetch=None, filter_value=None):
        """
        Fetches emails from the specified folder.

        Parameters:
            folder_name (str): The name of the folder (e.g., 'Inbox').
            top_fetch (int, optional): The number of emails to fetch.
            filter_value (str, optional): An OData filter string to filter the emails.

        Returns:
            list or None: A list of email objects if successful; otherwise, None.
        """
        folder_id = folder_name.lower() if folder_name.lower() == "inbox" else self.get_folder_id(folder_name)
        if folder_id is None:
            print(f"Folder '{folder_name}' not found.")
            return None

        print(f"Folder_id: {folder_id}")

        # Build query parameters.
        query_params = []
        if filter_value:
            query_params.append(f'$filter={filter_value}')
        if top_fetch:
            query_params.append(f'$top={top_fetch}')

        query_string = '?' + '&'.join(query_params) if query_params else ''
        endpoint = f'{GRAPH_ENDPOINT}{self.user_id}/mailFolders/{folder_id}/messages{query_string}'

        access_token = self.get_access_token()
        headers = {'Authorization': f'Bearer {access_token}'}
        r = requests.get(endpoint, headers=headers)
        if r.ok:
            return r.json().get('value', [])
        else:
            print_error_details('Failed to fetch emails', r)
            return None

    def get_email_attachments(self, email_internet_message_id):
        """
        Fetches the attachments of an email identified by its Internet message ID.

        Parameters:
            email_internet_message_id (str): The Internet message ID of the email.

        Returns:
            list: A list of attachment objects.

        Raises:
            Exception: If the email is not found or attachments cannot be fetched.
        """
        email_id = self.get_id_from_email(email_internet_message_id)
        if not email_id:
            raise Exception(f'Email with InternetMessageId {email_internet_message_id} not found.')

        endpoint = f'{GRAPH_ENDPOINT}{self.user_id}/messages/{email_id}/attachments'
        access_token = self.get_access_token()
        headers = {'Authorization': f'Bearer {access_token}'}
        r = requests.get(endpoint, headers=headers)
        if r.ok:
            return r.json().get('value', [])
        else:
            print_error_details('Failed to fetch attachments', r)
            raise Exception(
                f'Failed to fetch attachments for email with InternetMessageId: {email_internet_message_id}')

    def download_attachment(self, attachment_id, email_internet_message_id):
        """
        Downloads a specified attachment from an email.

        Parameters:
            attachment_id (str): The ID of the attachment.
            email_internet_message_id (str): The Internet message ID of the email.

        Returns:
            bytes or None: The content of the attachment as bytes.

        Raises:
            Exception: If the email or attachment cannot be found/downloaded.
        """
        email_id = self.get_id_from_email(email_internet_message_id)
        if not email_id:
            raise Exception(f'Email with InternetMessageId {email_internet_message_id} not found.')

        endpoint = f'{GRAPH_ENDPOINT}{self.user_id}/messages/{email_id}/attachments/{attachment_id}/$value'
        access_token = self.get_access_token()
        headers = {'Authorization': f'Bearer {access_token}'}
        r = requests.get(endpoint, headers=headers)
        if r.ok:
            return r.content
        else:
            print_error_details('Failed to download attachment', r)
            return None

    def send_csv_report(self, csv_content, csv_name, subject, body, emails):
        """
        Sends an email with a CSV report attached.

        Parameters:
            csv_content (str): The CSV content as a string.
            csv_name (str): The name for the CSV attachment.
            subject (str): The email subject.
            body (str): The email body content.
            emails (list): A list of recipient email addresses.

        Raises:
            Exception: If sending the email fails.
        """
        endpoint = f"{GRAPH_ENDPOINT}{self.user_id}/sendMail"
        access_token = self.get_access_token()

        email_msg = {
            "Message": {
                "Subject": subject,
                "Body": {
                    "ContentType": "Text",
                    "Content": body
                },
                "ToRecipients": [
                    {"EmailAddress": {"Address": recipient}}
                    for recipient in emails
                ],
                "Attachments": [
                    {
                        "@odata.type": "#microsoft.graph.fileAttachment",
                        "Name": csv_name,
                        "ContentBytes": base64.b64encode(csv_content.encode()).decode()
                    }
                ]
            },
            "SaveToSentItems": "true"
        }

        headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
        r = requests.post(endpoint, headers=headers, json=email_msg)
        if r.ok:
            print('Sent email successfully')
        else:
            raise Exception(f'Failed to send email: {r.status_code} - {r.json()}')

    def delete_email(self, email_internet_message_id):
        """
        Deletes an email identified by its Internet message ID.

        Parameters:
            email_internet_message_id (str): The Internet message ID of the email.

        Returns:
            bool: True if deletion is successful; otherwise, False.

        Raises:
            Exception: If the email is not found.
        """
        email_id = self.get_id_from_email(email_internet_message_id)
        if not email_id:
            raise Exception(f'Email with InternetMessageId {email_internet_message_id} not found.')

        endpoint = f'{GRAPH_ENDPOINT}{self.user_id}/messages/{email_id}'
        access_token = self.get_access_token()
        headers = {'Authorization': f'Bearer {access_token}'}
        response = requests.delete(endpoint, headers=headers)

        # A successful DELETE request returns HTTP 204 No Content.
        if response.status_code == 204:
            print('Email deleted successfully.')
            return True
        else:
            print_error_details('Failed to delete email', response)
            return False

    def get_email_mime(self, email_id):
        """
        Retrieves the raw MIME content of an email.
        """
        endpoint = f'{GRAPH_ENDPOINT}{self.user_id}/messages/{email_id}/$value'
        access_token = self.get_access_token()
        headers = {'Authorization': f'Bearer {access_token}'}
        r = requests.get(endpoint, headers=headers)
        if r.ok:
            return r.content
        else:
            print_error_details('Failed to fetch MIME content', r)
            return None
