import datetime
import os
import sys

class Logging:
    def __init__(self, bucket, store):
        logs_dir = os.getcwd() + '/logs'
        os.makedirs(logs_dir, exist_ok=True)
        self.execution_id = store + "_" + datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        self.file_path = os.path.join(logs_dir, f'{self.execution_id}.log')
        self.create_log_file()
        self.bucket = bucket

    def create_log_file(self):
        with open(self.file_path, 'w') as file:
            file.write(f'Execution ID: {self.execution_id}\n')
            file.write(f'Started at: {datetime.datetime.now()}\n')
            file.write('-'*30 + '\n')

    def _write_log(self, log, level):
        log_data = f'{datetime.datetime.now()}: {level} - {log}'
        with open(self.file_path, 'a') as file:
            file.write(f'{log_data}\n')
        print(log_data)

    def log(self, log):
        self._write_log(log, 'INFO')

    def debug(self, log):
        self._write_log(log, 'DEBUG')

    def error(self, log):
        self._write_log(log, 'ERROR')

    def __del__(self):
        with open(self.file_path, 'a') as file:
            file.write('-'*30 + '\n')
            file.write(f'Finished at: {datetime.datetime.now()}\n')
            file.write('='*30 + '\n')

        # Post to S3
        try:
            sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
            from invokeRPA.Utilities.boto3_utils import Boto3Utils

            boto3 = Boto3Utils()
            boto3.upload_file(self.file_path, self.bucket, f'logs/downloader/{self.execution_id}.log')
        except Exception as e:
            print(f'Error uploading log file to S3: {str(e)}')

