# This module contains utility functions for interacting with AWS services using the Boto3 library.

import json
import boto3
from botocore.exceptions    import ClientError
from urllib.parse import urlparse
import os
def get_secret(secret_name, return_json=True):
    """
    This function retrieves the secret value from AWS Secrets Manager.
    """
    region_name = "us-east-1"

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )

    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name
        )
    except ClientError as e:
        print(f"An error occurred while retrieving the secret value for {secret_name}: {e}")
        raise e

    secret = get_secret_value_response['SecretString']
    if return_json:
        return json.loads(secret)

    return secret



def post_to_s3(bucket, key, filename, file_path):
    # Upload the log message to S3
    s3 = boto3.client('s3', region_name='us-east-1')

    # Upload the log file to S3
    key_tmp = f"{key}/{filename}"
    s3.upload_file(file_path, bucket, key_tmp)

    presigned_url = s3.generate_presigned_url(
            'get_object', 
            Params={'Bucket': bucket, 'Key': key_tmp},
            ExpiresIn=(60 * 60 * 24 * 7)
        )
    
    return f"s3://{bucket}/{key}/{filename}"


def download_file_from_s3(s3_url, local_path):
    """
    Downloads a file from an S3 URL to a local path using boto3.
    
    Args:
        s3_url (str): The S3 URL (format: s3://bucket-name/path/to/file)
        local_path (str): The local path where the file should be saved
        
    Returns:
        bool: True if download was successful, False otherwise
    """
    try:
        # Parse the S3 URL to get bucket and key
        parsed_url = urlparse(s3_url)
        bucket_name = parsed_url.netloc
        s3_key = parsed_url.path.lstrip('/')
        
        # Initialize S3 client
        s3_client = boto3.client('s3')
        
        # Download the file
        s3_client.download_file(bucket_name, s3_key, local_path)
        
        return True
        
    except Exception as e:
        print(f"Error downloading file from S3: {e}")
        return False

def get_presigned_url(bucket_name, object_key):
    # Initialize S3 client
    s3_client = boto3.client('s3')

    print("ENTROO")

    # Generate the pre-signed URL (expires in 1 hour by default)
    presigned_url = s3_client.generate_presigned_url(
        'get_object',
        Params={'Bucket': bucket_name, 'Key': object_key},
        ExpiresIn=3600  # URL expiration time in seconds (1 hour)
    )

    return presigned_url