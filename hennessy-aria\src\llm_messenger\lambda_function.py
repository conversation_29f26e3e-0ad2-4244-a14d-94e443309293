from llm import LlmHandler
import json
import traceback

def lambda_handler(event, context):
    
    print(event)

    provider = event['provider'] #openai, bedrock
    host = event['host'] #azure, public
    llm_model = event['llm_model']
    prompt = event['prompt']
    message = event['message']
    s3_files = event.get('files', [])
    response_format = event.get('response_format', "")

    try:
        llm_handler = LlmHandler(provider, host, llm_model, response_format)
        response = llm_handler.send_message_llm(prompt, message, s3_files)
        return {
            "statusCode": 200,
            "body": json.dumps({"message": response})
        }
    
    except Exception as e:
        print(f"Error when reaching the response from llm: {traceback.format_exc()}")
        return {
            "statusCode": 500,
            "body": json.dumps({"message": f"Error when reaching the response from llm_ {e}"})
        }
