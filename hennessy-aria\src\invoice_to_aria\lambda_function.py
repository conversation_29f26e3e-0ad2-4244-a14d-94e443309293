
from boto3_utils import download_file_from_s3, get_presigned_url, trigger_lambda_response, generate_presigned_url
import os
from crud_invoices import CrudInvoices
from crud_report_rows import CrudReynolsReport
from crud_bols import CrudBols
from crud_titles import <PERSON><PERSON><PERSON><PERSON><PERSON>
from aria_utils import AriaUtils, TemporalUnavailableException
import base64
import json
import traceback
import datetime

def process_post_inventory_invoice(vin, vin_data, crud_report_rows):

    crud_bol = CrudBols()
    crud_title = CrudTitle()

    data_to_update_inv = {}

    # In order to have the date on the vin if it have the RO Date and we dont have the BOL date
    vin_data['ro_date'] = "" if str(vin_data['flows']["post-inventory"]["report-data"]['svc_ro_date']).lower() ==  "nan" else vin_data['flows']["post-inventory"]["report-data"]['svc_ro_date']
    vin_data['bol_date'] = ""

    print(f" ****** LOOKING FOR A BOL CONTAINING VIN... ******  ")
    # The logic here is that could be more than one bol with the VIN
    # We are going to get every time of more than one the first
    # If that bol doesnt have wi its cause it isnt processed by aria so we cant
    # send its information cause we dont have it yet.
    bol_containing_vin = crud_bol.find_bol_by_vin(vin)
    if bol_containing_vin is not None:
        if not isinstance(bol_containing_vin, dict):
            print("More than one BOL contaning the VIN ", bol_containing_vin)
            bol_containing_vin = bol_containing_vin[0]

        if bol_containing_vin.get('aria_wi_id', None) is not None:
            vin_data['bol_date'] = bol_containing_vin['fields']['bol_date']['value']

            presigned_url = get_presigned_url(os.environ['BUCKET'], f"{bol_containing_vin['path']}/{bol_containing_vin['attachment_name']}")
            vin_data['bol_url'] = presigned_url

            data_to_update_inv['attached_bol_id'] = bol_containing_vin['attachment_id']
            data_to_update_inv['attached_bol_wi_id'] = bol_containing_vin['aria_wi_id']

            crud_report_rows.update_row_with_bol_by_vin(vin=vin, bol=bol_containing_vin)

    title_containing_vin = crud_title.find_title_by_vin(vin)
    print("TITLE FOUND? ", title_containing_vin)
    if title_containing_vin is not None:
        if not isinstance(title_containing_vin, dict):
            print("More than one Title contaning the VIN ", title_containing_vin)
            title_containing_vin = title_containing_vin[0]

        if title_containing_vin.get('aria_wi_id', None) is not None and "complete" in title_containing_vin['status'].lower(): 
            
            try:

                rows = title_containing_vin['fields']['vins']['value'] or title_containing_vin['fields']['vins']['rows']
                pages = []
                for k, v in rows.items():
                    if v['cells']['vin']['value'] == vin:
                        pages = v['cells']['pages']['value'].split(",")
                        break
                            
                pages = list(map(int, pages))
                print("SEE THE PAGES SELECTED", pages)

                title_path = f"s3://{os.environ['BUCKET']}/{title_containing_vin['path']}/{title_containing_vin['title_name']}"
                
                print(" ***** PROCESSING IMAGES ***** ")
                print("title_path", title_path)
                print({"action": "process_batch_pages_document", "s3_file": title_path, "pages_to_extract": pages})
                pdf_processer_lambda_response = trigger_lambda_response(os.environ['PDF_PROCESSER_LAMBDA'], {"action": "process_batch_pages_document", "s3_file": title_path, "pages_to_extract": pages})
                if isinstance(pdf_processer_lambda_response, str):
                    pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response)

                print(f" ****** RESPONSE FROM PDF_PROCESSOR ****** ")
                print(pdf_processer_lambda_response)
                print("********************************************")

                if pdf_processer_lambda_response['statusCode'] != 200:
                    return {
                        "statusCode": 500,
                        "body": json.dumps({"message": "Error response from pdf_utils."})
                    }
                
                pdf_processer_lambda_response_body = json.loads(pdf_processer_lambda_response['body'])
                title_pages_to_be_concatenated = pdf_processer_lambda_response_body['splitted_images']

                print(" ***** CONCATENATING IMAGES ***** ")

                pdf_processer_lambda_response = trigger_lambda_response(os.environ['PDF_PROCESSER_LAMBDA'], {"action": "concat_images", "s3_files": title_pages_to_be_concatenated})
                if isinstance(pdf_processer_lambda_response, str):
                    pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response)

                print(f" ****** RESPONSE FROM PDF_PROCESSOR ****** ")
                print(pdf_processer_lambda_response)
                print("********************************************")

                if pdf_processer_lambda_response['statusCode'] != 200:
                    return {
                        "statusCode": 500,
                        "body": {
                            json.dumps({"message": "Error response from pdf_utils."})
                        }
                    }
                
                pdf_processer_lambda_response_body = json.loads(pdf_processer_lambda_response['body'])

                concated_title = pdf_processer_lambda_response_body['file_url']
                
                presigned_url = generate_presigned_url(concated_title)

                print("PRESINGED URL FOR TITLE", presigned_url)

                crud_report_rows.update_row_with_title_by_vin(vin, title_containing_vin)
                print("UPDATED DB")

                vin_data['title_url'] = presigned_url

                data_to_update_inv['attached_title_id'] = title_containing_vin['title_id']
                data_to_update_inv['attached_title_wi_id'] = title_containing_vin['aria_wi_id']

            except Exception as e:
                print(f"Error when linking the title to the invoice...Skipping...{traceback.format_exc()}")

    return data_to_update_inv


def lambda_handler(event, context):

    vin = event.get('vin', None)
    if vin is None:
        return "No vin provided. Aborting..."
    
    crud_invoices = CrudInvoices()
    crud_report_rows = CrudReynolsReport()


    try:

        stage = event.get("stage", None)
        if stage is None or stage == "":
            return {
                'statusCode': 500,
                'body': {
                    "message": json.dumps(f"Error no stage provide!")
                }
            }
        aria_app_id = ""
        if stage == "post-inventory":
            aria_app_id = os.environ['ARIA_APP_ID_POST_INVENTORY']
        elif stage == "pre-inventory":
            aria_app_id = os.environ['ARIA_APP_ID_PRE_INVENTORY']
        elif stage == "used-cars":
            aria_app_id = os.environ['ARIA_APP_ID_USED_CARS']
        
        aria_utils = AriaUtils(app_id=aria_app_id)

        print(f" ****** PROCESSING VIN {vin} ****** ")

        invoice_data = crud_invoices.find_invoice_vin_and_stage(vin, stage)
        vin_data = crud_report_rows.find_report_row_by_vin(vin)

        if ((vin_data is None or invoice_data is None) and stage != "used-cars"):
            return {
                "Aborting, vin not found on DB"
            }

        file_content = ""
        if invoice_data is not None:
            s3_file_path = f"s3://{os.environ['BUCKET']}/{invoice_data['file_path']}"
            filename = f"{vin}.pdf"
            file_local_path = f"/tmp/{filename}"
            download_file_from_s3(s3_file_path, file_local_path)

            with open(file_local_path, 'rb') as file:
                file_content = file.read()

            print(f" ****** DOWNLOADED VIN {s3_file_path} ******  ")

        if stage == "post-inventory":
            data_to_update_inv = process_post_inventory_invoice(vin=vin, vin_data=vin_data, crud_report_rows=crud_report_rows)
        elif stage == "pre-inventory":
            data_to_update_inv = {}
        elif stage == "used-cars":
            data_to_update_inv = {}

        
        print(f" ****** SENDING VIN TO ARIA {vin} ******  ")
        
        wi_id = ""
        status = None
        try:

            if stage == "post-inventory":
                aria_utils.construct_create_request_post_inventory(vin_data, base64.b64encode(file_content).decode('utf-8'))
                status = 4
            elif stage == "pre-inventory":
                aria_utils.construct_create_request_pre_inventory(vin_data, base64.b64encode(file_content).decode('utf-8'))
                status = 4
            elif stage == "used-cars":
                aria_utils.construct_create_request_used_car(vin_data, base64.b64encode(file_content).decode('utf-8') if file_content != "" else "")
                status = 4

            work_item_data = aria_utils.send_post_request()
            wi_id = work_item_data['id']
        except TemporalUnavailableException as e:
            wi_id = ""
        except Exception as e:
            print(f"Error processing vin when sending to Aria {vin}: {traceback.format_exc()}")
            return {
                'statusCode': 500,
                'body': json.dumps({"message": f"Error processing vin when sending to Aria: {e}"})
            }
        

        print(f" ****** SENT VIN TO ARIA {vin} ******  ")

        print(f" ****** WI DATA {wi_id} ***** ")
        data_to_update_inv['aria_wi_id'] = wi_id
        data_to_update_inv['sent_to_aria'] = True
        data_to_update_inv['aria_app_id'] = aria_app_id
        crud_invoices.update_invoice_vin(vin=vin, data = data_to_update_inv)
        crud_report_rows.update_row_by_vin(vin, 
            {
                f"status": status,
                f"flows.{stage}.docs.invoice.aria_data.aria_wi_id": wi_id,
                f"flows.{stage}.docs.invoice.aria_data.aria_app_id": aria_app_id,
                f"flows.{stage}.docs.invoice.aria_data.created": datetime.datetime.now()
            }, 
            {f"status_history": status})

        print(f" ****** UPDATED VIN IN DB {vin} ******  ")

        return {
            "statusCode": 202,
            "body": json.dumps({"message": "VIN processed correctly"})
        }
    
    except Exception as e:
        print(f"Error when sending invoice to Aria: {traceback.format_exc()}")
        return {
            "statusCode": 500,
            "body": json.dumps({"message": f"Error when sending invoice to Aria: {e}"})
        }