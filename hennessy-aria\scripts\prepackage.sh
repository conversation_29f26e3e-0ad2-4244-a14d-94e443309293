#!/bin/bash

echo "=== Preparing Lambda ZIPs ==="
echo "=== Running prepackage.sh ===" > prepackage.log
echo "Script executed at: $(date)" >> prepackage.log
echo "=== Preparing Lambda ZIPs ==="
# Directorios base
LAMBDA_SRC_DIR="src"
OUTPUT_DIR="artifacts"

# Create directory if not exists
rm -rf "$OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR"

# Clone needed functions to the src directory
EXTERNAL_LAMBDAS=(
  "external_modules/orchestrator_utilities/processes/rpa_processes_handler/:python_handler"
)

# Iterating over the external lambdas
for LAMBDA_PAIR in "${EXTERNAL_LAMBDAS[@]}"; do
  IFS=':' read -r EXTERNAL_PATH LAMBDA_NAME <<< "$LAMBDA_PAIR"
  DEST_DIR="$LAMBDA_SRC_DIR/$LAMBDA_NAME"
  mkdir -p "$DEST_DIR"
  echo "Cloning $EXTERNAL_PATH to $DEST_DIR..."
  # Copy the external lambda to the src directory
  if [ -d "$EXTERNAL_PATH" ]; then
    mkdir -p "$DEST_DIR"
    cp -r "$EXTERNAL_PATH"/* "$DEST_DIR"
    echo "Copied $EXTERNAL_PATH to $DEST_DIR"
  else
    echo "Directory $EXTERNAL_PATH does not exist. Skipping $LAMBDA_NAME."
  fi
done


# Hennessy lambdas
LAMBDAS=("email_watcher" "move_email" "process_email" "bre_handler" "bre" "llm_messenger" "pdf_utils" "llm_extractor" \
"reynols_report_processor" "invoice_downloader" "invoice_to_aria" "title_sftp_to_s3_to_aria" "reconciliate" "orchestrator_downloader" \
"orchestrator_download_update" "reevaluate" "python_handler" "loading_wi_report" "error_wi_report" "report_loaded_data" \
"queues_handler" "report_pages_not_used_in_titles" "lambda_authorizer" "secrets_handler" "report_to_aria" "completed_vins_validation" \
"load_pricing_guide" "load_pricing_guide_extractor" "bre_used_cars" "llm_extractor_used_cars" "pre_stock_in_vins" "llm_extractor_pre_inventory" \
"bre_pre_inventory" )

for LAMBDA in "${LAMBDAS[@]}"; do
  ZIP_FILE="$OUTPUT_DIR/${LAMBDA}.zip"
  SRC_DIR="$LAMBDA_SRC_DIR/$LAMBDA"

  echo "Creating ZIP for $LAMBDA..."

  if [ -d "$SRC_DIR" ]; then
    (cd "$SRC_DIR" && zip -r "../../$ZIP_FILE" . -x "**/__pycache__/**")
    echo "Created $ZIP_FILE"
  else
    echo "Directory $SRC_DIR does not exist. Skipping $LAMBDA."
  fi
done

echo "=== Lambda ZIPs created ==="
