import json
import boto3


class Boto3Utilities:
    def __init__(self):
        self.client = boto3.client('secretsmanager')
        
    def create_secret(self, name, value):
        try:
            if isinstance(value, dict):
                value = json.dumps(value)
            elif isinstance(value, int):
                value = str(value)
            response = self.client.create_secret(
                Name=name,
                SecretString=value
            )
            if response.get('ResponseMetadata',''):
                return True if response['ResponseMetadata'].get('HTTPStatusCode') == 200 else False
            else:
                return False
        except Exception as e:
            print(f'Error: {e}')
            return False
            
    def get_secret(self, name, value):
        try:
            response = self.client.get_secret_value(
                SecretId=name
            )
            if response.get('ResponseMetadata',''):
                return response['SecretString'] if response['ResponseMetadata'].get('HTTPStatusCode') == 200 else ''
            else:
                return ''            
        except Exception as e:
            print(f'Error: {e}')
            return ''
            
    def delete_secret(self, name, value):
        try:
            response = self.client.delete_secret(
                SecretId=name,
                RecoveryWindowInDays=7
            )
            if response.get('ResponseMetadata',''):
                return True if response['ResponseMetadata'].get('HTTPStatusCode') == 200 else False
            else:
                return False            
        except Exception as e:
            print(f'Error: {e}')
            return False
        
    def update_secret(self, name, value):
        try:
            response = self.client.update_secret(
                SecretId=name,
                SecretString=value
            )
            if response.get('ResponseMetadata',''):
                return True if response['ResponseMetadata'].get('HTTPStatusCode') == 200 else False
            else:
                return False            
        except Exception as e:
            print(f'Error: {e}')
            return False