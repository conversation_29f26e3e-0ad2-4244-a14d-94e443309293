from crud_report_rows   import <PERSON>rud<PERSON>ey<PERSON>lsReport
from boto3_utils import post_to_s3, get_latest_csv_metadata, download_csv_from_s3
from datetime           import datetime
import pandas           as pd
import traceback
import json
import os
from aria_utils import AriaUtils

def get_if_exists(obj, key, default=None):
    """
    Get the value of a key in a dictionary if it exists, otherwise return the default value.
    """
    key_parts = key.split('.')
    for part in key_parts:
        if obj is None:
            return default
        obj = obj.get(part, None)
    return obj

def post_report_to_s3(stage, report_type, detailed_file_name, tmp_file_path):
    now_date = datetime.now()
    bucket_name = os.environ['BUCKET']
    s3_key = f"reports"
    year = str(now_date.year)
    month = str(now_date.month)
    day =  str(now_date.day)
    hour = str(now_date.hour)  
    minute = str(now_date.minute)

    aria_file_name = ""
    if "download" in report_type:
        aria_file_name = f"Report VINs status {stage}"
    elif "reconciliate" in report_type:
        aria_file_name = f"Reconciliation {stage}"
    elif "stock_in" in report_type:
        aria_file_name = f"Stock in Reynolds {stage}"
    elif "discarded" in report_type:
        aria_file_name = f"VINs discarded after 60 days {stage}"
    elif "wrong_flow" in report_type:
        aria_file_name = f"VINs with wrong flow {stage}"
        
    detailed_file_name = f"{datetime.now()}_{detailed_file_name}"

    folder_path = f"{s3_key}/{year}/{month}/{day}"
    return aria_file_name, post_to_s3(bucket_name, folder_path, detailed_file_name, tmp_file_path)


def generate_aria_report(report_type, vins_to_report, stage):

    aria = AriaUtils()
    crud_report_rows = CrudReynolsReport()

    vins_to_make_report = crud_report_rows.get_vins_info_of_selected(vins_to_report)

    STATUS_MAP = {
        0: 'Pending Invoice Download',
        1: 'Invoice Not Found',
        2: 'Invoice Download Error',
        3: 'Pending to be Sent to ARIA',
        4: 'Sent to ARIA',
        5: "Pre-processed in ARIA",
        6: "Needs Human intervention",
        7: 'Ready to DMS',
        9: 'To be retried in Reynolds',
        10: 'Unexpected error when doing stock in, in Reynolds',
        11: 'Completed (Reynolds success)',
        12: 'Expired download'
    }   

    df = None
    if stage == "post-inventory":
        df = pd.DataFrame([{
            'STORE': get_if_exists(vin, 'flows.post-inventory.report-data.store'),
            'RECEIVED': get_if_exists(vin, 'flows.post-inventory.report-data.received'),
            'STOCK': get_if_exists(vin, 'flows.post-inventory.report-data.stock'),
            'MAKE': get_if_exists(vin, 'flows.post-inventory.report-data.make'),
            'DESC': get_if_exists(vin, 'flows.post-inventory.report-data.desc'),
            'VIN': vin['vin'],
            'INV AMT': get_if_exists(vin, 'flows.post-inventory.report-data.inv_amt'),
            'SLS COST': get_if_exists(vin, 'flows.post-inventory.report-data.sls_cost'),
            'STK IN NOTES': get_if_exists(vin, 'flows.post-inventory.report-data.stock_in_notes'),
            'SVC RO DATE': get_if_exists(vin, 'flows.post-inventory.report-data.svc_ro_date'),
            'STAT-CODE': get_if_exists(vin, 'flows.post-inventory.report-data.stat_code'),
            'JRNL-PURCH-DATE-DR': get_if_exists(vin, 'flows.post-inventory.report-data.jrnl_purch_date_dr'),
            'STATUS': get_if_exists(vin, 'flows.post-inventory.docs.invoice.aria_data.status') if get_if_exists(vin, 'flows.post-inventory.docs.invoice.aria_data.status') is not None else STATUS_MAP[vin['status']],
            'LOADED IN ARIA': get_if_exists(vin, 'flows.post-inventory.docs.invoice.aria_data.created'),
        } for vin in vins_to_make_report])
    elif stage == "pre-inventory":
        df = pd.DataFrame([{
            'STORE': get_if_exists(vin, 'flows.pre-inventory.report-data.store'),
            'VIN': vin['vin'],
            'MODEL CODE': get_if_exists(vin, 'flows.pre-inventory.report-data.model_code'),
            'MODEL DESCRIPTION': get_if_exists(vin, 'flows.pre-inventory.report-data.model_description'),
            'COLOR': get_if_exists(vin, 'flows.pre-inventory.report-data.color'),
            'COMMISSION NUMBER': get_if_exists(vin, 'flows.pre-inventory.report-data.commission_number'),
            'STATUS': get_if_exists(vin, 'flows.pre-inventory.docs.invoice.aria_data.status') if get_if_exists(vin, 'flows.pre-inventory.docs.invoice.aria_data.status') is not None else STATUS_MAP[vin['status']],
            'LOADED IN ARIA': get_if_exists(vin, 'flows.pre-inventory.docs.invoice.aria_data.created'),
        } for vin in vins_to_make_report])
    elif stage == "used-cars":
        df = pd.DataFrame([{
            'STORE': get_if_exists(vin, 'flows.used-cars.report-data.store'),
            'VIN': vin['vin'],
            'STOCK': get_if_exists(vin, 'flows.used-cars.report-data.stock'),
            'RECEIVED': get_if_exists(vin, 'flows.used-cars.report-data.received'),
            'STATUS': get_if_exists(vin, 'flows.used-cars.docs.invoice.aria_data.status') if get_if_exists(vin, 'flows.used-cars.docs.invoice.aria_data.status') is not None else STATUS_MAP[vin['status']],
            'LOADED IN ARIA': get_if_exists(vin, 'flows.used-cars.docs.invoice.aria_data.created'),
        } for vin in vins_to_make_report])

    if report_type == "wrong_flow":
        df = pd.DataFrame([{
            'VIN': vin['vin'],
            'STATUS': STATUS_MAP[vin['status']],
            'ACTUAL FLOW': get_if_exists(vin, 'current_flow'),
        } for vin in vins_to_make_report])


    file_name = "vins_rep.xlsx"
    file_path = f"/tmp/{file_name}"

    df.to_excel(file_path, engine='openpyxl', index=False)

    aria_name, presigned_url = post_report_to_s3(stage, report_type, file_name, file_path)

    aria.construct_create_request(aria_name, presigned_url)
    aria.send_post_request()

    return {
        'statusCode': 200,
        'body': json.dumps({'message': "Report processed correctly"}, default=str)
    }


def lambda_handler(event, context):
    
    print("**** EVENT ****")
    print(event)
    event = event['body']

    if isinstance(event, str):
        event = json.loads(event)

    if isinstance(event, str):
        event = json.loads(event)

    vins_to_make_report = event.get("vins", [])
    report_type = event.get("type")
    stage = event.get("stage")

    if stage is None or stage == "":
        return {
            'statusCode': 500,
            'body': {
                "message": json.dumps(f"Error no stage provide!")
            }
        }
    
    report_folder = ""
    if stage == "post-inventory":
        report_folder = os.environ['REYNOLS_REPORT_FOLDER']
    elif stage == "pre-inventory":
        pass
    elif stage == "used-cars":
        report_folder = os.environ['USED_CARS_REPORTS']

    if len(vins_to_make_report) == 0:
        filename = get_latest_csv_metadata(os.environ['BUCKET'], report_folder)

        if filename is None:
            print("No report found")
            return {
                "statusCode": 200,
                "body": json.dumps({"message": "No report found"})
            }
        
        latest_file_path = download_csv_from_s3(os.environ['BUCKET'], filename)
        print(f" ****** DOWNLOADED REPORT: {filename['Key']} ****** ")
        
        reynols_report_df = pd.read_csv(latest_file_path)
        vins_to_make_report = reynols_report_df['VIN'].values.tolist()

    try: 

        return generate_aria_report(report_type, vins_to_make_report, stage)

    except Exception:
        print("Exception: ", traceback.format_exc())
        return {
            'statusCode': 500,
            'body': json.dumps({'error': f"Error when generating report: {traceback.format_exc()}"}, default=str)
        }

    