from Utilities.Common_Utilities.logger_utility import Logger
import time
import pyautogui
import pyperclip
import traceback

class Gui_utilities():

    def __init__(self, log_path):
        self.l = Logger(log_path)

    #Send keys
    #   INPUT
    #       -text
    #       -interval: seconds between each key
    def send_keys(self, text, interval):
        try:
            pyautogui.write(text, interval=interval)
        except Exception as e:            
            raise Exception ('Exception occurred on send_keys method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    #Press special keys
    #   INPUT
    #       -key
    def press_special_keys(self, key):
        try:
            pyautogui.hotkey(key)
            time.sleep(0.5)
        except Exception as e:            
            raise Exception ('Exception occurred on press_special_keys method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    #Set text into clipboard
    #   INPUT
    #       -text
    def set_clipboard(self, text):
        try:
            pyperclip.copy(text)
            time.sleep(5)
        except Exception as e:           
            raise Exception ('Exception occurred on set_clipboard method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    #Paste clipboard
    def paste_clipboard(self):
        try:
            time.sleep(0.1)
            pyautogui.keyDown('ctrl')
            time.sleep(0.5)
            pyautogui.press('v')
            time.sleep(0.5)
            pyautogui.keyUp('ctrl')

        except Exception as e:          
            raise Exception ('Exception occurred on paste_clipboard method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))