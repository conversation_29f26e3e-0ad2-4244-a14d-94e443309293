from Utilities.Common_Utilities.logger_utility import Logger
from Utilities.Common_Utilities.http_utility import Http
import traceback

class Mongo():

    def __init__(self, log_path, token, url):
        self.l = Logger(log_path)
        self.http = Http(log_path)
        self.token = token
        self.url = url
        
    # Execute query
    #   INPUT:
    #       -json
    def execute_query(self, json):
        try:
            response = self.http.http_request('POST', self.url, json, self.token)
            return response.text
        except Exception as e:
            raise Exception ('Exception occurred on execute_query method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))