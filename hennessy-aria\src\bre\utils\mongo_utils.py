import pymongo

class Mongo():
    def __init__(self, mongo_uri):
        self.client = pymongo.MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        self.db = self.col = ''

    def select_db_and_collection(self, db_name, collection_name):
        self.db = self.client[db_name]
        self.col = self.db.get_collection(collection_name)

    def insert_one(self, data):
        self.col.insert_one(data)
        
    def insert_many(self, data):
        self.col.insert_many(data)
        
    def update_one(self, filter, data):
        self.col.update_one(filter, data) 

    def update(self, filter, data):
        self.col.update(filter, data)        
        
    def update_many(self, filter, data):
        self.col.update_many(filter, data)         
        
    def find_one(self, data):
        x = self.col.find_one(data)
        return x
    
    def find(self, data):
        x = self.col.find(data)
        return x
    
    def count_documents(self, data):
        x = self.col.count_documents(data)
        return x

    def drop_collection(self, collection_name):
        self.db.get_collection(collection_name).drop()

    def create_collection(self, collection_name):
        self.db.create_collection(collection_name)
     
    def close_connection(self):
        self.client.close()