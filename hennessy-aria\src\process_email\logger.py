import io
import os
import logging
import datetime

from boto3_utils import post_to_s3

class Logger:
    def __init__(self):
        # Configure logging format
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        log_formatter = logging.Formatter(log_format)

        # Create a StringIO object to capture log messages
        self.log_stream = io.StringIO()
        log_handler = logging.StreamHandler(self.log_stream)
        log_handler.setFormatter(log_formatter)

        # Configure root logger to use the StringIO handler
        self.root_logger = logging.getLogger()
        self.root_logger.addHandler(log_handler)
        self.root_logger.setLevel(logging.INFO)
        
    def get_root_logger(self):
        return self.root_logger
        
    def print_log_msg(self, log_message, type=0):
        print(log_message)
        if type == 0:
            logging.info(log_message)
        elif type == 1:
            logging.error(log_message)

    def send_log_to_s3(self, function_name):
        try:
            log_message = self.log_stream.getvalue().splitlines()

            # Get the bucket name and S3 key
            bucket_name = os.environ['ENV'] + "-aria-corpay"
            s3_key = f"logs/{function_name}"

            # Get the current timestamp
            timestamp = datetime.datetime.now()

            # Create the folder structure
            year = timestamp.strftime("%Y")
            month = timestamp.strftime("%m")
            day = timestamp.strftime("%d")

            folder_path = f"{s3_key}/{year}/{month}/{day}"

            # Create the log file name
            log_file_name = timestamp.strftime("%H_%M_%S_%f") + ".log"

            # Convert the log data to a string
            log_message = "\n".join(log_message)

            post_to_s3(log_message, bucket_name, folder_path, log_file_name)

            self.print_log_msg(
                f"Send log to s3 function: Log sent to S3: s3://{bucket_name}/{folder_path}/{log_file_name}")
        except Exception as e:
            self.print_log_msg(f"Error sending log to S3: {str(e)}", 1)