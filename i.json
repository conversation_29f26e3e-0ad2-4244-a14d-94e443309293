{
    "tag_titles": {
        "bill_of_sale": {
            "fields": {
                "deal_number": {
                    "required": true,
                    "type": "string",
                    "validation_rules": [
                        "not_empty",
                        "alphanumeric"
                    ],
                    "error_msg": "Deal number is required and should be alphanumeric"
                },
                "stock_number": {
                    "required": true,
                    "type": "string",
                    "validation_rules": [
                        "not_empty"
                    ],
                    "cross_document_match": [
                        "mv1.vehicle_info.stock_number"
                    ],
                    "error_msg": "Stock number is required and should match MV1"
                },
                "vin": {
                    "required": true,
                    "type": "string",
                    "validation_rules": [
                        "vin_format",
                        "vin_checksum"
                    ],
                    "cross_document_match": [
                        "mv1.vehicle_info.vin",
                        "mv7d.vehicle_info.vin",
                        "title_mso.vehicle_info.vin",
                        "dealer_dmv.vehicle_info.vin"
                    ],
                    "database_validation": [
                        "vin_exists",
                        "vin_not_duplicate"
                    ],
                    "error_msg": "VIN is required, should be valid format and checksum, and should not be duplicate in current batch"
                },
                "year": {
                    "required": true,
                    "type": "integer",
                    "validation_rules": [
                        "year_range"
                    ],
                    "error_msg": "Year is required and should be within reasonable range",
                    "cross_document_match": [
                        "mv1.vehicle_info.year",
                        "mv7d.vehicle_info.year",
                        "title_mso.vehicle_info.year"
                    ]
                },
                "make": {
                    "required": true,
                    "type": "string",
                    "validation_rules": [
                        "not_empty"
                    ],
                    "error_msg": "Make is required and should not be empty",
                    "cross_document_match": [
                        "mv1.vehicle_info.make",
                        "mv7d.vehicle_info.make",
                        "title_mso.vehicle_info.make"
                    ]
                },
                "model": {
                    "required": true,
                    "type": "string",
                    "validation_rules": [
                        "not_empty"
                    ],
                    "error_msg": "Model is required and should not be empty",
                    "cross_document_match": [
                        "mv1.vehicle_info.model",
                        "mv7d.vehicle_info.model",
                        "title_mso.vehicle_info.model"
                    ]
                },
                "odometer_reading": {
                    "required": false,
                    "type": "integer",
                    "validation_rules": [
                        "positive_number"
                    ],
                    "error_msg": "Odometer reading should be a positive number and should match across documents",
                    "cross_document_match": [
                        "mv1.vehicle_info.odometer_reading",
                        "mv7d.vehicle_info.odometer_reading",
                        "title_mso.vehicle_info.odometer_reading"
                    ]
                },
                "buyer_name": {
                    "required": true,
                    "type": "string",
                    "validation_rules": [
                        "name_format"
                    ],
                    "error_msg": "Buyer name is required and should be in valid name format and should match across documents",
                    "cross_document_match": [
                        "driver_license.full_name",
                        "mv1.owner_info.buyer_full_name",
                        "mv7d.buyer_info.buyer_name"
                    ]
                },
                "co_buyer_name": {
                    "required": false,
                    "type": "string",
                    "validation_rules": [
                        "name_format"
                    ],
                    "cross_document_match": [
                        "mv1.owner_info.co_buyer_name"
                    ],
                    "error_msg": "Co-buyer name should be in valid name format and should match across documents"
                },
                "buyer_address": {
                    "required": true,
                    "type": "string",
                    "validation_rules": [
                        "address_format"
                    ],
                    "cross_document_match": [
                        "driver_license.address",
                        "mv1.owner_info.buyer_address"
                    ],
                    "error_msg": "Buyer address is required and should be in valid address format and should match across documents"
                },
                "sale_price": {
                    "required": true,
                    "type": "decimal",
                    "validation_rules": [
                        "positive_amount"
                    ],
                    "cross_document_match": [
                        "mv1.sale_details.sales_price"
                    ],
                    "database_validation": [
                        "tavt_calculation"
                    ],
                    "error_msg": "Sale price is required and should be a positive amount and should match across documents"
                },
                "trade_in_value": {
                    "required": false,
                    "type": "decimal",
                    "validation_rules": [
                        "positive_amount"
                    ],
                    "error_msg": "Trade-in value should be a positive amount"
                },
                "tavt_tax_amount": {
                    "required": true,
                    "type": "decimal",
                    "validation_rules": [
                        "positive_amount"
                    ],
                    "cross_document_match": [
                        "dealer_dmv.tavt_amount"
                    ],
                    "database_validation": [
                        "tavt_calculation_match"
                    ],
                    "error_msg": "TAVT tax amount is required and should be a positive amount and should match across documents"
                },
                "total_amount_due": {
                    "required": false,
                    "type": "decimal",
                    "validation_rules": [
                        "positive_amount"
                    ],
                    "error_msg": "Total amount due should be a positive amount"
                },
                "lien_holder_name": {
                    "required": false,
                    "type": "string",
                    "validation_rules": [
                        "name_format"
                    ],
                    "error_msg": "Lien holder name should be in valid name format and should match across documents",
                    "cross_document_match": [
                        "mv1.title_lien_info.lien_holder_name",
                        "title_mso.lien_info.lien_holder_name"
                    ]
                }
            }
        },
    "driver_license": {
        "display_name": "Driver's License",
        "description": "Identity verification document",
        "sections": {
            "personal_info": {
                "fields": {
                    "full_name": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "name_format"
                        ],
                        "cross_document_match": [
                            "bill_of_sale.buyer_info.buyer_name",
                            "mv1.owner_info.buyer_full_name"
                        ]
                    },
                    "date_of_birth": {
                        "required": true,
                        "type": "date",
                        "validation_rules": [
                            "valid_date",
                            "age_verification"
                        ]
                    },
                    "address": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "address_format"
                        ],
                        "cross_document_match": [
                            "bill_of_sale.buyer_info.buyer_address",
                            "mv1.owner_info.buyer_address"
                        ]
                    },
                    "city": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "not_empty"
                        ]
                    },
                    "state": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "state_code"
                        ]
                    },
                    "zip": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "zip_format"
                        ]
                    },
                    "license_number": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "license_format"
                        ],
                        "cross_document_match": [
                            "mv1.owner_info.customer_id"
                        ]
                    },
                    "expiration_date": {
                        "required": false,
                        "type": "date",
                        "validation_rules": [
                            "valid_date",
                            "not_expired"
                        ]
                    }
                }
            }
        }
    },
    "mv1": {
        "display_name": "MV-1 Title Application",
        "description": "Official Georgia state form for vehicle registration",
        "sections": {
            "owner_info": {
                "fields": {
                    "buyer_full_name": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "name_format"
                        ],
                        "cross_document_match": [
                            "driver_license.personal_info.full_name",
                            "bill_of_sale.buyer_info.buyer_name"
                        ]
                    },
                    "co_buyer_name": {
                        "required": false,
                        "type": "string",
                        "validation_rules": [
                            "name_format"
                        ],
                        "cross_document_match": [
                            "bill_of_sale.buyer_info.co_buyer_name"
                        ]
                    },
                    "buyer_address": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "address_format"
                        ],
                        "cross_document_match": [
                            "driver_license.personal_info.address",
                            "bill_of_sale.buyer_info.buyer_address"
                        ]
                    },
                    "county_of_residence": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "county_format"
                        ],
                        "database_validation": [
                            "county_tax_jurisdiction"
                        ]
                    },
                    "customer_id": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "license_format"
                        ],
                        "cross_document_match": [
                            "driver_license.personal_info.license_number"
                        ]
                    }
                }
            },
            "vehicle_info": {
                "fields": {
                    "vin": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "vin_format",
                            "vin_checksum"
                        ],
                        "cross_document_match": [
                            "bill_of_sale.vehicle_info.vin",
                            "mv7d.vehicle_info.vin",
                            "title_mso.vehicle_info.vin"
                        ]
                    },
                    "year": {
                        "required": true,
                        "type": "integer",
                        "validation_rules": [
                            "year_range"
                        ],
                        "cross_document_match": [
                            "bill_of_sale.vehicle_info.year",
                            "mv7d.vehicle_info.year",
                            "title_mso.vehicle_info.year"
                        ]
                    },
                    "make": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "not_empty"
                        ],
                        "cross_document_match": [
                            "bill_of_sale.vehicle_info.make",
                            "mv7d.vehicle_info.make",
                            "title_mso.vehicle_info.make"
                        ]
                    },
                    "model": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "not_empty"
                        ],
                        "cross_document_match": [
                            "bill_of_sale.vehicle_info.model",
                            "mv7d.vehicle_info.model",
                            "title_mso.vehicle_info.model"
                        ]
                    },
                    "body_style": {
                        "required": false,
                        "type": "string",
                        "validation_rules": [
                            "not_empty"
                        ]
                    },
                    "odometer_reading": {
                        "required": true,
                        "type": "integer",
                        "validation_rules": [
                            "positive_number"
                        ],
                        "cross_document_match": [
                            "bill_of_sale.vehicle_info.odometer_reading",
                            "mv7d.vehicle_info.odometer_reading",
                            "title_mso.vehicle_info.odometer_reading"
                        ]
                    }
                }
            },
            "title_lien_info": {
                "fields": {
                    "lien_holder_name": {
                        "required": false,
                        "type": "string",
                        "validation_rules": [
                            "name_format"
                        ],
                        "cross_document_match": [
                            "bill_of_sale.lien_info.lien_holder_name",
                            "title_mso.lien_info.lien_holder_name"
                        ]
                    },
                    "lien_holder_address": {
                        "required": false,
                        "type": "string",
                        "validation_rules": [
                            "address_format"
                        ]
                    }
                }
            },
            "dealer_info": {
                "fields": {
                    "dealer_name": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "name_format"
                        ]
                    },
                    "dealer_license_id": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "dealer_license_format"
                        ],
                        "database_validation": [
                            "valid_dealer_license"
                        ]
                    }
                }
            },
            "sale_details": {
                "fields": {
                    "sales_price": {
                        "required": true,
                        "type": "decimal",
                        "validation_rules": [
                            "positive_amount"
                        ],
                        "cross_document_match": [
                            "bill_of_sale.sale_tax_info.sale_price"
                        ]
                    }
                }
            }
        }
    },
    "mv7d": {
        "display_name": "MV-7D Reassignment Form",
        "description": "Georgia motor vehicle reassignment form (Red Form)",
        "sections": {
            "vehicle_info": {
                "fields": {
                    "vin": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "vin_format",
                            "vin_checksum"
                        ],
                        "cross_document_match": [
                            "mv1.vehicle_info.vin",
                            "bill_of_sale.vehicle_info.vin",
                            "title_mso.vehicle_info.vin"
                        ]
                    },
                    "year": {
                        "required": true,
                        "type": "integer",
                        "validation_rules": [
                            "year_range"
                        ],
                        "cross_document_match": [
                            "mv1.vehicle_info.year",
                            "bill_of_sale.vehicle_info.year",
                            "title_mso.vehicle_info.year"
                        ]
                    },
                    "make": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "not_empty"
                        ],
                        "cross_document_match": [
                            "mv1.vehicle_info.make",
                            "bill_of_sale.vehicle_info.make",
                            "title_mso.vehicle_info.make"
                        ]
                    },
                    "model": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "not_empty"
                        ],
                        "cross_document_match": [
                            "mv1.vehicle_info.model",
                            "bill_of_sale.vehicle_info.model",
                            "title_mso.vehicle_info.model"
                        ]
                    },
                    "odometer_reading": {
                        "required": true,
                        "type": "integer",
                        "validation_rules": [
                            "positive_number"
                        ],
                        "cross_document_match": [
                            "mv1.vehicle_info.odometer_reading",
                            "bill_of_sale.vehicle_info.odometer_reading",
                            "title_mso.vehicle_info.odometer_reading"
                        ]
                    },
                    "odometer_type": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "odometer_type_enum"
                        ],
                        "enum_values": [
                            "Actual",
                            "Exceeds",
                            "Not Actual"
                        ]
                    }
                }
            },
            "buyer_info": {
                "fields": {
                    "buyer_name": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "name_format"
                        ],
                        "cross_document_match": [
                            "mv1.owner_info.buyer_full_name",
                            "driver_license.personal_info.full_name",
                            "bill_of_sale.buyer_info.buyer_name"
                        ]
                    },
                    "buyer_address": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "address_format"
                        ],
                        "cross_document_match": [
                            "mv1.owner_info.buyer_address",
                            "driver_license.personal_info.address"
                        ]
                    }
                }
            },
            "date_fields": {
                "fields": {
                    "date_of_reassignment": {
                        "required": true,
                        "type": "date",
                        "validation_rules": [
                            "valid_date",
                            "reasonable_date_range"
                        ]
                    }
                }
            },
            "certification": {
                "fields": {
                    "seller_signature": {
                        "required": true,
                        "type": "boolean",
                        "validation_rules": [
                            "signature_present"
                        ]
                    },
                    "buyer_signature": {
                        "required": true,
                        "type": "boolean",
                        "validation_rules": [
                            "signature_present"
                        ]
                    }
                }
            }
        }
    },
    "title_mso": {
        "display_name": "Title/MSO",
        "description": "Official ownership document (Title for used, MSO for new vehicles)",
        "sections": {
            "vehicle_info": {
                "fields": {
                    "vin": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "vin_format",
                            "vin_checksum"
                        ],
                        "cross_document_match": [
                            "mv1.vehicle_info.vin",
                            "bill_of_sale.vehicle_info.vin",
                            "mv7d.vehicle_info.vin"
                        ]
                    },
                    "year": {
                        "required": true,
                        "type": "integer",
                        "validation_rules": [
                            "year_range"
                        ],
                        "cross_document_match": [
                            "mv1.vehicle_info.year",
                            "bill_of_sale.vehicle_info.year",
                            "mv7d.vehicle_info.year"
                        ]
                    },
                    "make": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "not_empty"
                        ],
                        "cross_document_match": [
                            "mv1.vehicle_info.make",
                            "bill_of_sale.vehicle_info.make",
                            "mv7d.vehicle_info.make"
                        ]
                    },
                    "model": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "not_empty"
                        ],
                        "cross_document_match": [
                            "mv1.vehicle_info.model",
                            "bill_of_sale.vehicle_info.model",
                            "mv7d.vehicle_info.model"
                        ]
                    },
                    "body_style": {
                        "required": false,
                        "type": "string",
                        "validation_rules": [
                            "not_empty"
                        ]
                    },
                    "odometer_reading": {
                        "required": true,
                        "type": "integer",
                        "validation_rules": [
                            "positive_number"
                        ],
                        "cross_document_match": [
                            "mv1.vehicle_info.odometer_reading",
                            "bill_of_sale.vehicle_info.odometer_reading",
                            "mv7d.vehicle_info.odometer_reading"
                        ]
                    },
                    "selling_dealer_name": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "name_format"
                        ]
                    }
                }
            },
            "lien_info": {
                "fields": {
                    "lien_holder_name": {
                        "required": false,
                        "type": "string",
                        "validation_rules": [
                            "name_format"
                        ],
                        "cross_document_match": [
                            "mv1.title_lien_info.lien_holder_name",
                            "bill_of_sale.lien_info.lien_holder_name"
                        ]
                    },
                    "lien_satisfied": {
                        "required": false,
                        "type": "boolean",
                        "validation_rules": [
                            "boolean_value"
                        ]
                    }
                }
            },
            "assignment_section": {
                "fields": {
                    "date_of_transfer": {
                        "required": true,
                        "type": "date",
                        "validation_rules": [
                            "valid_date",
                            "reasonable_date_range"
                        ]
                    },
                    "buyer_name": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "name_format"
                        ],
                        "cross_document_match": [
                            "mv1.owner_info.buyer_full_name",
                            "bill_of_sale.buyer_info.buyer_name"
                        ]
                    },
                    "seller_signature": {
                        "required": true,
                        "type": "boolean",
                        "validation_rules": [
                            "signature_present"
                        ]
                    }
                }
            },
            "title_info": {
                "fields": {
                    "title_number": {
                        "required": true,
                        "type": "string",
                        "validation_rules": [
                            "title_number_format"
                        ],
                        "database_validation": [
                            "title_number_valid"
                        ]
                    }
                }
            }
        }
    }
    }
}

,
            "lien_holder_address": {
               "required": false,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "address_format",
                     "isValidationType": "Regex",
                     "regex": "^[0-9]+\\s+[A-Za-z0-9\\s,.-]+,\\s*[A-Za-z\\s]+,\\s*[A-Z]{2}\\s+[0-9]{5}(-[0-9]{4})?$",
                     "error_msg": "Lien holder address should be in valid format (Street, City, State ZIP)"
                  }
               ]
            },
            "dealer_name": {
               "required": true,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "not_empty",
                     "isValidationType": "Regex",
                     "regex": "^.{1,}$",
                     "error_msg": "Dealer name is required"
                  },
                  {
                     "check": "business_name_format",
                     "isValidationType": "Regex",
                     "regex": "^[A-Za-z0-9\\s&.,'-]+$",
                     "error_msg": "Dealer name should contain only valid business name characters"
                  }
               ]
            },
            "dealer_license_number": {
               "required": true,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "not_empty",
                     "isValidationType": "Regex",
                     "regex": "^[A-Z0-9]{6,12}$",
                     "error_msg": "Dealer license number is required"
                  },
                  {
                     "check": "license_format",
                     "isValidationType": "Regex",
                     "regex": "^[A-Z0-9]{6,12}$",
                     "error_msg": "Dealer license number should be 6-12 alphanumeric characters"
                  }
               ]
            },
            "dealer_address": {
               "required": true,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "not_empty",
                     "isValidationType": "Regex",
                     "regex": "^.{1,}$",
                     "error_msg": "Dealer address is required"
                  },
                  {
                     "check": "address_format",
                     "isValidationType": "Regex",
                     "regex": "^[0-9]+\\s+[A-Za-z0-9\\s,.-]+,\\s*[A-Za-z\\s]+,\\s*[A-Z]{2}\\s+[0-9]{5}(-[0-9]{4})?$",
                     "error_msg": "Dealer address should be in valid format (Street, City, State ZIP)"
                  }
               ]
            },
            "sale_date": {
               "required": true,
               "type": "date",
               "validation_rules": [
                  {
                     "check": "not_empty",
                     "isValidationType": "Date",
                     "date_format": "MM/DD/YYYY",
                     "error_msg": "Sale date is required"
                  },
                  {
                     "check": "date_range",
                     "isValidationType": "Date",
                     "min_date": "01/01/2000",
                     "max_date": "today",
                     "error_msg": "Sale date should be between 2000 and today"
                  }
               ]
            },
            "buyer_phone": {
               "required": false,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "phone_format",
                     "isValidationType": "Regex",
                     "regex": "^\\(?([0-9]{3})\\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$",
                     "error_msg": "Phone number should be in valid format (XXX) XXX-XXXX"
                  }
               ]
            },
            "buyer_email": {
               "required": false,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "email_format",
                     "isValidationType": "Regex",
                     "regex": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
                     "error_msg": "Email should be in valid format"
                  }
               ]
            },
            "buyer_state": {
               "required": true,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "not_empty",
                     "isValidationType": "Country",
                     "country": "US",
                     "field_type": "state",
                     "us_states": [
                        "AL",
                        "AK",
                        "AZ",
                        "AR",
                        "CA",
                        "CO",
                        "CT",
                        "DE",
                        "FL",
                        "GA",
                        "HI",
                        "ID",
                        "IL",
                        "IN",
                        "IA",
                        "KS",
                        "KY",
                        "LA",
                        "ME",
                        "MD",
                        "MA",
                        "MI",
                        "MN",
                        "MS",
                        "MO",
                        "MT",
                        "NE",
                        "NV",
                        "NH",
                        "NJ",
                        "NM",
                        "NY",
                        "NC",
                        "ND",
                        "OH",
                        "OK",
                        "OR",
                        "PA",
                        "RI",
                        "SC",
                        "SD",
                        "TN",
                        "TX",
                        "UT",
                        "VT",
                        "VA",
                        "WA",
                        "WV",
                        "WI",
                        "WY"
                     ],
                     "error_msg": "Buyer state is required"
                  },
                  {
                     "check": "state_code",
                     "isValidationType": "Country",
                     "country": "US",
                     "field_type": "state",
                     "format": "abbreviation",
                     "error_msg": "State should be valid US state abbreviation",
                     "us_states": [
                        "AL",
                        "AK",
                        "AZ",
                        "AR",
                        "CA",
                        "CO",
                        "CT",
                        "DE",
                        "FL",
                        "GA",
                        "HI",
                        "ID",
                        "IL",
                        "IN",
                        "IA",
                        "KS",
                        "KY",
                        "LA",
                        "ME",
                        "MD",
                        "MA",
                        "MI",
                        "MN",
                        "MS",
                        "MO",
                        "MT",
                        "NE",
                        "NV",
                        "NH",
                        "NJ",
                        "NM",
                        "NY",
                        "NC",
                        "ND",
                        "OH",
                        "OK",
                        "OR",
                        "PA",
                        "RI",
                        "SC",
                        "SD",
                        "TN",
                        "TX",
                        "UT",
                        "VT",
                        "VA",
                        "WA",
                        "WV",
                        "WI",
                        "WY"
                     ]
                  }
               ]
            },
            "buyer_zip_code": {
               "required": true,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "not_empty",
                     "isValidationType": "Regex",
                     "regex": "^[0-9]{5}(-[0-9]{4})?$",
                     "error_msg": "ZIP code is required"
                  },
                  {
                     "check": "zip_format",
                     "isValidationType": "Regex",
                     "regex": "^[0-9]{5}(-[0-9]{4})?$",
                     "error_msg": "ZIP code should be in format XXXXX or XXXXX-XXXX"
                  }
               ]
            },
            "vehicle_color": {
               "required": true,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "not_empty",
                     "isValidationType": "Regex",
                     "regex": "^[A-Za-z\\s]+$",
                     "error_msg": "Vehicle color is required"
                  },
                  {
                     "check": "color_format",
                     "isValidationType": "Regex",
                     "regex": "^[A-Za-z\\s]{2,20}$",
                     "error_msg": "Vehicle color should be 2-20 alphabetic characters"
                  }
               ]
            },
            "vehicle_body_style": {
               "required": false,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "body_style_format",
                     "isValidationType": "Regex",
                     "regex": "^[A-Za-z0-9\\s-]{2,30}$",
                     "error_msg": "Body style should be 2-30 characters"
                  }
               ]
            },
            "engine_size": {
               "required": false,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "engine_format",
                     "isValidationType": "Regex",
                     "regex": "^[0-9]\\.[0-9]L?$|^[0-9]{3,4}cc$|^V[0-9]{1,2}$",
                     "error_msg": "Engine size should be in format like 2.0L, 1500cc, or V8"
                  }
               ]
            },
            "fuel_type": {
               "required": false,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "fuel_type_format",
                     "isValidationType": "Regex",
                     "regex": "^(Gasoline|Diesel|Electric|Hybrid|CNG|LPG)$",
                     "error_msg": "Fuel type should be one of: Gasoline, Diesel, Electric, Hybrid, CNG, LPG"
                  }
               ]
            },
            "transmission_type": {
               "required": false,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "transmission_format",
                     "isValidationType": "Regex",
                     "regex": "^(Manual|Automatic|CVT|Semi-Automatic)$",
                     "error_msg": "Transmission should be Manual, Automatic, CVT, or Semi-Automatic"
                  }
               ]
            },
            "warranty_info": {
               "required": false,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "warranty_format",
                     "isValidationType": "Regex",
                     "regex": "^[A-Za-z0-9\\s,.-]{0,200}$",
                     "error_msg": "Warranty info should be up to 200 characters"
                  }
               ]
            },
            "financing_company": {
               "required": false,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "company_name_format",
                     "isValidationType": "Regex",
                     "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$",
                     "error_msg": "Financing company name should be 2-100 characters"
                  }
               ]
            },
            "down_payment": {
               "required": false,
               "type": "decimal",
               "validation_rules": [
                  {
                     "check": "positive_amount",
                     "isValidationType": "Numeric",
                     "min_value": 0,
                     "max_value": 999999.99,
                     "error_msg": "Down payment should be positive amount"
                  }
               ]
            },
            "monthly_payment": {
               "required": false,
               "type": "decimal",
               "validation_rules": [
                  {
                     "check": "positive_amount",
                     "isValidationType": "Numeric",
                     "min_value": 0,
                     "max_value": 99999.99,
                     "error_msg": "Monthly payment should be positive amount"
                  }
               ]
            },
            "loan_term_months": {
               "required": false,
               "type": "integer",
               "validation_rules": [
                  {
                     "check": "loan_term_range",
                     "isValidationType": "Numeric",
                     "min_value": 12,
                     "max_value": 84,
                     "error_msg": "Loan term should be between 12 and 84 months"
                  }
               ]
            },
            "interest_rate": {
               "required": false,
               "type": "decimal",
               "validation_rules": [
                  {
                     "check": "interest_rate_range",
                     "isValidationType": "Numeric",
                     "min_value": 0,
                     "max_value": 30,
                     "error_msg": "Interest rate should be between 0% and 30%"
                  }
               ]
            },
            "buyer_drivers_license": {
               "required": true,
               "type": "string",
               "validation_rules": [
                  {
                     "check": "not_empty",
                     "isValidationType": "Regex",
                     "regex": "^[A-Za-z0-9]{6,20}$",
                     "error_msg": "Driver's license number is required"
                  },
                  {
                     "check": "license_format",
                     "isValidationType": "Regex",
                     "regex": "^[A-Za-z0-9]{6,20}$",
                     "error_msg": "Driver's license should be 6-20 alphanumeric characters"
                  }
               ]
            },
            "buyer_date_of_birth": {
               "required": true,
               "type": "date",
               "validation_rules": [
                  {
                     "check": "not_empty",
                     "isValidationType": "Date",
                     "date_format": "MM/DD/YYYY",
                     "error_msg": "Date of birth is required"
                  },
                  {
                     "check": "age_verification",
                     "isValidationType": "Date",
                     "min_age": 18,
                     "max_age": 120,
                     "error_msg": "Buyer must be between 18 and 120 years old"
                  }
               ]
            }