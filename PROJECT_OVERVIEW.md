# Hennessy Automotive Invoice Processing System

## 🚗 Project Overview

This is a comprehensive automotive invoice processing and automation system designed for multiple car dealership brands. The system automates the download, processing, and management of invoices from various automotive vendor portals.

## 🏗️ Architecture & Technology Stack

### **Technology Stack:**
- **Backend**: Python 3.11, Node.js
- **Cloud Platform**: AWS (Lambda, S3, ECR, Step Functions)
- **Infrastructure**: Serverless Framework v3
- **Database**: MongoDB
- **Web Automation**: Selenium WebDriver, Undetected Chrome Driver
- **AI/ML**: OpenAI GPT, AWS Bedrock
- **Document Processing**: PDF utilities, OCR
- **Containerization**: Docker

### **Project Structure:**
```
Invoke/
├── hennessy/                    # Main RPA automation service (Python)
├── hennessy-aria/              # Serverless AWS Lambda functions (Node.js/Python)
└── hennessy-rpa-reynolds/      # Reynolds report processing
```

## 🎯 What This System Does

### **Main Functions:**
1. **Automated Invoice Download**: Scrapes invoices from automotive vendor portals
2. **Document Processing**: Extracts data from PDFs and images using AI
3. **Business Rules Engine (BRE)**: Processes and validates invoice data
4. **Email Processing**: Monitors and processes incoming emails
5. **Report Generation**: Creates reconciliation and execution reports
6. **Data Integration**: Syncs with Aria ERP system

### **Supported Automotive Brands:**
- **Ford** (FOR)
- **Lexus** (LOA, LOG) 
- **Land Rover/Jaguar** (JLRN, JLRB, JLRG)
- **Porsche** (POR, PNW)
- **Cadillac** (CAD)

## 🚀 Getting Started

### **Prerequisites:**
```bash
# Required Software
- Python 3.11+
- Node.js 16+
- Docker
- AWS CLI
- Serverless Framework v3
```

### **1. Environment Setup:**

#### **Install Global Dependencies:**
```bash
# Install Serverless Framework v3
npm install -g serverless@3

# Install LocalStack for local testing
pip install localstack

# Install AWS CLI (if not already installed)
# Follow: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html
```

#### **Configure AWS Credentials:**
```bash
aws configure
# Enter your AWS Access Key ID, Secret Access Key, and region (us-east-1)
```

### **2. Project Setup:**

#### **Hennessy (Main RPA Service):**
```bash
cd hennessy

# Install Python dependencies
pip install -r requirements.txt

# Set up environment variables (create .env file)
cp .env.example .env  # Edit with your credentials

# Test locally
python app.py
```

#### **Hennessy-Aria (Serverless Functions):**
```bash
cd hennessy-aria

# Install Node.js dependencies
npm install

# Install Python dependencies for Lambda layers
pip install -r layers/requirements.txt -t layers/python/
```

### **3. Local Development with LocalStack:**

```bash
# Start LocalStack
localstack start

# Create S3 bucket for deployments
aws s3 mb s3://ach-deployment-bucket-local --endpoint-url=http://localhost:4566

# Deploy to local environment
npm run deploy:local
```

### **4. Testing the System:**

#### **Test Invoice Download:**
```python
# Example: Download Ford invoices
event = {
    "store": "FOR",
    "vins": ["1FTFW1ET5DFC12345", "1FTFW1ET5DFC67890"]
}

# This will:
# 1. Login to Ford portal
# 2. Search for invoices by VIN
# 3. Download invoice PDFs
# 4. Upload to S3
```

#### **Test Lambda Functions:**
```bash
# Test BRE handler
serverless invoke --stage local --function bre_handler --data '{
    "body": {
        "vin": "1FTFW1ET5DFC12345",
        "store": "FOR"
    }
}'

# Test LLM extractor
serverless invoke --stage local --function llm_extractor --data '{
    "provider": "openai",
    "host": "public", 
    "llm_model": "gpt-4",
    "prompt": "Extract invoice data",
    "message": "Process this invoice"
}'
```

## 🔧 Configuration

### **Environment Variables:**
```bash
# AWS Configuration
AWS_REGION=us-east-1
AWS_PROFILE=your-profile

# Database
MONGO_DATABASE=hennessy_dev
MONGODB_URI=mongodb://your-mongo-connection

# Vendor Credentials (stored in AWS Secrets Manager)
FORD_USERNAME=your-ford-username
FORD_PASSWORD=your-ford-password
# ... (similar for other vendors)

# AI Services
OPENAI_API_KEY=your-openai-key
BEDROCK_REGION=us-east-1
```

### **Vendor Portal Configuration:**
Each vendor requires specific credentials and configuration:

```python
# Example: Ford configuration
FORD_CONFIG = {
    "login_url": "https://ford-dealer-portal.com/login",
    "username": "your-username",
    "password": "your-password",
    "invoice_search_path": "/invoices/search"
}
```

## 📊 API Examples

### **1. Download Invoices API:**
```python
# POST /download-invoices
{
    "store": "FOR",           # Store code
    "vins": [                 # Vehicle Identification Numbers
        "1FTFW1ET5DFC12345",
        "1FTFW1ET5DFC67890"
    ],
    "action": "invoice_download"
}

# Response:
{
    "statusCode": 200,
    "downloaded_files": [
        "s3://bucket/invoices/FOR_1FTFW1ET5DFC12345.pdf",
        "s3://bucket/invoices/FOR_1FTFW1ET5DFC67890.pdf"
    ]
}
```

### **2. Process Document API:**
```python
# POST /process-document
{
    "action": "process_document",
    "s3_path": "s3://bucket/invoices/invoice.pdf",
    "extract_fields": ["invoice_number", "amount", "date", "vin"]
}

# Response:
{
    "statusCode": 200,
    "extracted_data": {
        "invoice_number": "INV-12345",
        "amount": "$25,000.00",
        "date": "2024-01-15",
        "vin": "1FTFW1ET5DFC12345"
    }
}
```

### **3. Business Rules Engine API:**
```python
# POST /bre-handler
{
    "body": {
        "vin": "1FTFW1ET5DFC12345",
        "store": "FOR",
        "invoice_data": {
            "amount": 25000,
            "date": "2024-01-15"
        }
    }
}

# Response:
{
    "statusCode": 200,
    "bre_result": "approved",
    "next_action": "send_to_aria",
    "execution_id": "uuid-12345"
}
```

## 🔄 Workflow Example

### **Complete Invoice Processing Flow:**

1. **Trigger**: Email received or scheduled job
2. **Download**: System logs into vendor portal and downloads invoices
3. **Extract**: AI extracts data from PDF invoices
4. **Validate**: Business rules engine validates the data
5. **Process**: Data is processed and sent to Aria ERP
6. **Report**: Execution report is generated

```mermaid
graph TD
    A[Email/Schedule Trigger] --> B[Login to Vendor Portal]
    B --> C[Search & Download Invoices]
    C --> D[Upload to S3]
    D --> E[AI Data Extraction]
    E --> F[Business Rules Engine]
    F --> G[Send to Aria ERP]
    G --> H[Generate Report]
```

## 🚀 Deployment

### **Deploy to AWS:**
```bash
# Deploy to staging
npm run deploy:snd

# Deploy to production  
npm run deploy:prd

# Deploy single function
npm run deploy:snd:function --function=bre_handler
```

### **Docker Deployment:**
```bash
# Build and deploy container
cd hennessy
./build_deploy.sh
```

## 📝 Next Steps

1. **Set up your vendor credentials** in AWS Secrets Manager
2. **Configure MongoDB connection** 
3. **Test with sample VINs** for each supported brand
4. **Set up monitoring** and alerts
5. **Configure email processing** rules
6. **Set up scheduled jobs** for automated processing

## 🆘 Support

- Check logs in AWS CloudWatch
- Use LocalStack dashboard: https://app.localstack.cloud/dashboard
- Monitor Lambda functions in AWS Console
- Check MongoDB for data validation

## 🔐 Security Notes

- All vendor credentials are stored in AWS Secrets Manager
- VPC configuration isolates Lambda functions
- IAM roles follow least privilege principle
- All data is encrypted in transit and at rest
