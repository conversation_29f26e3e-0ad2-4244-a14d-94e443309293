from Utilities.Common_Utilities.logger_utility import Logger
import requests
import traceback
import json

class Http():

    def __init__(self,log_path):
        self.l = Logger(log_path)

    #HTTP request
    #   INPUT
    #       -method: POST...
    #       -url
    #       -body
    #       -authorization
    def http_request(self, method, url, body, authorization = None):
        try:
            headers = {"Authorization": authorization} if authorization != None else {}
            match method:
                case 'POST':
                    result = requests.post(url, json = json.loads(body) if isinstance(body, str) else body, headers = headers)
                    return result
                case _:
                    raise ("System Exception: Invalid HTTP Method")
        except Exception as e:            
            raise Exception('Exception occurred on extract_regex_group method. Details: ' + str(e) + ' More info: ' + str(traceback.format_exc()))