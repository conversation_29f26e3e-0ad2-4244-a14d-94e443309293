# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret

"""
Folios Schema:
{
    # BASIC INFO
    "aria_wi_id": "string",
    "aria_app_id": "string",
    "email_id": "string",
    "attachment_name": "string",
    "attachment_id": "string",
    "is_digital": "boolean",
    # DATA TO BE EXTRACTED
    "folio_data": {
        "extracted_data": {
            "string": "string"
        },
        "act_data": {
            "string": "string"
        }
    },
    # FOLIO INFO
    "created_at": "datetime",
    "updated_at": "datetime",
    "folio_status": "string",
    "status_history": [
        "string"
    ]
}
"""

class CrudBols:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name = 'bol'


    def insert_bol(self, email_id, attachment_id, attachment_name, path, file_ocr, wi_id):
        """
        This function inserts a folio into the database.
        """

        bol_document = {
            "email_id": email_id,
            "attachment_name": attachment_name,
            "attachment_id": attachment_id,
            "path": path,
            "raw_ocr": file_ocr,
            "aria_wi_id": wi_id,
            "read_at": datetime.datetime.now(),
            "status_history": ["Pending"]
        }
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.insert_one(bol_document)

    def find_bol_by_id(self, attachment_id):
        """
        This function finds a bol by its attachment ID.
        """
        query = {"attachment_id": attachment_id}
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)
    
    def find_bol_by_email_id(self, bol_id):
        """
        This function finds a bol by its ID.
        """
        query = {"aria_wi_id": bol_id}
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find_one(query)
    
    def find_bol_using_vin(self, vin):
        """
        This function finds a bol by its ID.
        """
        query = {"extracted_vins": vin}
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find(query)

    def find_bols_by_filter(self, filter):
        """
        This function finds bols by a filter.
        """
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return list(self.mongo.find(filter))
    
    def __del__(self):
        self.mongo.close_connection()