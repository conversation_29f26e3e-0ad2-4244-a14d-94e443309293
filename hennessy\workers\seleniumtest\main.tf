variable "aws_region" {
  type = string
}


module "seleniumtest" {
  source                                = "../../terraform/modules/lambda"
  project_name                          = "seleniumtest"
  aws_region                            = var.aws_region
  environments                          = ["dev", "prod"]
  lambda_memory_size                    = 4096
  lambda_reserved_concurrent_executions = 1
  environment_variables = {
    dev = {
      TEST_VARIABLE = var.test_variable
    }
    prod = {
      TEST_VARIABLE = var.test_variable
    }
  }
}
