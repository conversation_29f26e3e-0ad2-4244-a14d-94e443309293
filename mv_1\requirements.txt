# AWS Lambda Dependencies for Document Processing
# Core Python packages (most are included in Lambda runtime)

# Data processing and JSON handling
dataclasses-json==0.6.3

# Type hints and validation
typing-extensions==4.8.0

# Optional: If you need additional JSON processing
orjson==3.9.10

# Optional: For enhanced data validation
pydantic==2.5.0

# Optional: For more advanced OCR confidence calculations
numpy==1.24.3

# Optional: For statistical analysis of processing results
scipy==1.11.4

# Development and testing dependencies (not needed in Lambda)
pytest==7.4.3
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0

# Note: The following packages are already available in AWS Lambda Python runtime:
# - json (built-in)
# - os (built-in) 
# - traceback (built-in)
# - typing (built-in)
# - enum (built-in)
# - dataclasses (Python 3.7+)
