
from boto3_utils import get_latest_files, download_file_from_s3
import os
from crud_report_rows import CrudReynolsReport
from crud_invoices import CrudInvoices
from datetime import datetime
import json
import traceback

def lambda_handler(event, context):

    crud_reynols_report = CrudReynolsReport()
    crud_invoices = CrudInvoices()
    vins_to_send_to_aria = []

    try:

        stage = event.get("stage", None)
        if stage is None or stage == "":
            return {
                'statusCode': 500,
                'body': {
                    "message": json.dumps(f"Error no stage provide!")
                }
            }

        print(" ****** GETTING INVOICES TO PROCESS ****** ")

        invoices_folder = ""
        if stage == "post-inventory":
            invoices_folder = os.environ['INVOICES_FOLDER']
            print("STAGE: POST-INVENTORY")
        elif stage == "pre-inventory":
            invoices_folder = os.environ['INVOICES_FOLDER_PRE_INVENTORY']
            print("STAGE: PRE-INVENTORY")
        elif stage == "used-cars":
            invoices_folder = os.environ['INVOICES_FOLDER_USED_CARS']
            print("STAGE: USED-CARS")


        today = datetime.today()
        year = today.strftime("%Y") 
        month = today.strftime("%m") 
        day = today.strftime("%d") 
        
        invoices_folder_path = f"{invoices_folder}/{year}/{month}/{day}"
        invoices_files = get_latest_files(os.environ['BUCKET'], invoices_folder_path)

        if (not invoices_files or len(invoices_files) == 0) and stage != "used-cars":
            print("No invoices found in the S3 bucket.")
            return {
                'statusCode': 200,
                'body': {
                    "message": json.dumps(f"No invoices found in the S3 bucket."),
                    "vins_to_be_processed": vins_to_send_to_aria 
                }
            }
        
        if invoices_files is not None:
            for file in invoices_files:

                file_path = download_file_from_s3(os.environ['BUCKET'], file)
                if not file_path:
                    print(f"Failed to download the file: {file} from S3.")
                    continue

                
                filename = file_path.split("/")[-1]
                vin = filename[:filename.rfind(".")]

                print(f" ****** PROCESSING INVOICE: {vin} ****** ")
                
                existing_row = crud_reynols_report.find_report_row_by_vin(vin)

                if existing_row is None:
                    print("This vin should be being processed")
                    continue

                if len(list(existing_row["flows"][stage]["docs"]["invoice"].get('aria_data', {}).keys())) == 0:
                    print(f" ****** SENDING TO ARIA: {vin} ****** ")
                    crud_invoices.insert_invoice(vin, file['Key'], stage)
                    vins_to_send_to_aria.append(vin)
                    crud_reynols_report.update_row_by_vin(vin, {"status": 3}, {"status_history": 3})

        if stage == "used-cars":
            vins_without_invoice = crud_reynols_report.get_vins_used_cars_without_invoice()
            for vin_data in vins_without_invoice:
                vins_to_send_to_aria.append(vin_data["vin"])
                crud_reynols_report.update_row_by_vin(vin_data["vin"], {"status": 3}, {"status_history": 3})

        return {
            'statusCode': 200,
            'body': {"vins_to_be_processed": vins_to_send_to_aria}
        }

    except Exception as e:
        print(f"Error when downloading invoices from s3: {traceback.format_exc()}")
        return {
            'statusCode': 500,
            'body': {
                "vins_to_be_processed": vins_to_send_to_aria,
                "message": json.dumps(f"Error when downloading invoices from s3: {e}")
            }
        }