# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os
from datetime import datetime
from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudTitles:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='title'
        )

    def insert_title(self, uuid, time_uploaded, file_path_s3, filename , wi_id):
        """
        This function inserts a folio into the database.
        """

        title_document = {
            "title_id": str(uuid),
            "path": file_path_s3,
            "title_name": filename,
            "aria_wi_id": wi_id,
            "aria_app_id": os.environ['ARIA_APP_ID'],
            "read_at": datetime.now(),
            "sftp_uploaded_time": time_uploaded,
            "status_history": ["Pending"]
        }

        self.mongo.insert_one(title_document)

    def find_title_by_name_and_uploaded_time(self, title_name, upload_time):
        filter = { "title_name": { "$regex": title_name, "$options": "i" }, "sftp_uploaded_time": upload_time }
        return self.mongo.find_one(filter)

    def __del__(self):
        self.mongo.close_connection()