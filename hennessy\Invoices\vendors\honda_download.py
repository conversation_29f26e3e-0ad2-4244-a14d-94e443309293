import re
import time
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
import invokeRPA
from Invoices.downloader    import Downloader
from datetime import datetime, timedelta
from Invoices.otp_reader    import OtpReader
import traceback

class HONDA(Downloader):
    def __init__(self, store, current_flow):
        super().__init__(store, current_flow)
        # vin
        self.vin = ''
        self.main_url = 'https://www.in.honda.com/RRAAApps/login/asp/rraalog.asp?url=https%3A%2F%2Fwww.in.honda.com%2F'
        # Ford page is blocking chrome in headless mode
        if self.chrome.headless:
            self.send_headers()
        self.chrome.navigate_to_url(self.main_url)
        self.brand = "Honda"
        self.label = self.brand.lower()

        self.tracker = {}
        self.vin_list = []

    def login(self, user, password, dealer_code, otp):
        """
        Login process:
          - Navigates to the login page.
          - Fills in the dealer number, user ID, and password fields.
          - Submits the login form.
          - When the MFA page appears, it retrieves the access code from an email.
          - Types the MFA code into the field and verifies.
        """
        self.stage = 'LOGIN'
        try:
            self._log_info('Navigating to login page...')

            # Fill in login fields using IDs defined on the page
            self.chrome.type_value('ID', 'txtDlrNo', dealer_code)
            self.chrome.type_value('ID', 'txtLogonID', user)
            self.chrome.type_value('ID', 'txtPassword', password, True)
            self._log_info('Submitting login form...')
            self.chrome.click_button('ID', 'btnLogon')

            if self.chrome.get_element('ID', 'reselectMessage', error=False):
                self._log_info("MFA page detected. Selecting OTP method...")
                self.chrome.click_button('ID','reselectMessage')
                self.chrome.click_button('ID','totp')
                self.chrome.click_button('ID','chooseButton')
                # Fill otp
                self._log_info("Retrieving MFA code from OTP...")
                code = OtpReader(otp).get_otp()
                self._log_info(f'OTP code: {code}')
                self.chrome.type_value('XPATH', '//input[@id="otppswd"]', code, True)
                self.chrome.click_button('XPATH', '//button[@id="verifyButton"]')
            self.utilities.sleep(3)
            self._log_info("Login successful.")
        except Exception as e:
            print(traceback.format_exc())
            self._handle_error(e)
            raise e
        
    def send_headers(self):
        self.chrome.run_cmd("Network.setExtraHTTPHeaders", {
            "headers": {
                "Referer": "https://www.in.honda.com/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "DNT": "1",
                "Accept-Language": "en-US,en;q=0.9"
            }
        })

        # Erase webdriver from the website
        self.chrome.run_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });
            """
        })

    def parse_mfa_email(self, body):
        """
        Parses the MFA email body to extract the MFA code.

        Args:
            body (str): The email body.

        Returns:
            str: The extracted MFA code.
        """
        match = re.search(r'(\d{6})\s+is your MFA', body)
        if match:
            return match.group(1)
        else:
            return None

    def get_pipeline_report(self):
        """
        Pipeline Report process:
          - Assumes the user is already logged in.
          - Navigates through the Business Office > Invoices and Statements > Vehicle Pricing area.
          - Selects the "Full Pricing and Allowance Schedule" link.
          - Initiates the download of the PDF report.
        """
        self.stage = 'PIPELINE_REPORT'
        try:
            self._log_info('Navigating to pipeline report...')

            # Click Business Office menu item
            if not self.chrome.click_button('XPATH', '//td[contains(normalize-space(string(.)), "EXECUTIVE") and contains(normalize-space(string(.)), "MANAGEMENT")]'):
                raise Exception("Executive Management menu not found.")
            self.utilities.sleep(2)

            # Click Invoices and Statements link
            if not self.chrome.click_button('XPATH', '//table[.//td[text()="Vehicle Status"]]'):
                raise Exception("Vehicle Status menu not found.")
            self.utilities.sleep(2)

            self.chrome.switch_frame("Content")
            # Select checkboxes
            assert self.chrome.click_button('ID', 'ctl00_ctl00_M_content_stopSaleVINOnlyRadioButton_2')
            assert self.chrome.click_button('ID', 'ctl00_ctl00_M_content_isInTransitCheckBox')
            assert self.chrome.click_button('ID', 'ctl00_ctl00_M_content_isOnHandCheckBox')

            # Select Days Inv
            self.chrome.select_option_by_value('ID', 'ctl00_ctl00_M_content_daysInventoryDropDownList','030')
            # Click on Submit
            if not self.chrome.click_button('ID', 'ctl00_ctl00_M_content_submitButton'):
                raise Exception("Submit button not found.")
            self.utilities.sleep(10)

            # Click on Export as excel
            download_button = self.chrome.get_element('XPATH', '//a[contains(@onclick, "ExportToExcel")]')
            try:
                self.chrome.run_script('arguments[0].click();', download_button)
            except Exception as e:
                self._log_error(f"Error clicking download button: {e}")
                raise e

            self._log_info("Initiating download for pipeline report...")
            today = self.utilities.get_today()
            if self.get_file_from_download(
                extension='.csv',
                name=f'{self.brand}_{self.store}_pipeline_report_{today}',
                s3_path='pipeline_report/',
                stage='SAVE_PIPELINE_REPORT'
            ):
                self._log_info('Pipeline report downloaded!')
                return True
        except Exception as e:
            self._handle_error(e)
            raise e

    def download_vins(self,vins):
        self.navigate_to_search()
        return super().download_vins(vins)

    def search_by_vin(self, vin, count):

        self.vin = vin

        self.stage = 'SEARCH_INVOICE'

        self.chrome.switch_frame("Content")

        if not self.chrome.get_element('XPATH', '/html/body/form/div[1]/table[2]/tbody/tr/td[2]/input'):
            return False
        
        self.chrome.click_button('XPATH', '/html/body/form/div[1]/table[2]/tbody/tr/td[2]/input')
        self.utilities.sleep(1)

        if not self.chrome.get_element('XPATH', '//*[@id="forBorderIE10"]/tbody/tr[4]/td[2]/input'):
            return False
        
        self.chrome.type_value('XPATH', '//*[@id="forBorderIE10"]/tbody/tr[4]/td[2]/input', vin)
        self.utilities.sleep(1)

        if not self.chrome.get_element('XPATH', '//*[@id="SearchFunc"]/div[1]/table[3]/tbody/tr/td/input[1]'):
            return False
        
        self.chrome.click_button('XPATH', '//*[@id="SearchFunc"]/div[1]/table[3]/tbody/tr/td/input[1]')
        self.utilities.sleep(1)

        if not self.chrome.click_button('XPATH', '//table/tbody/tr/td/input[@type="button" and @value="Vehicle Invoice"]'):
            return False
        self.utilities.sleep(1)


        response = True
        if not self.chrome.click_button('XPATH', f'//table/tbody/tr[td[text()="{vin}"]]/td[a]/a'):
            response = False
        
        time.sleep(2)
        self._log_info('VIN found!')
        self.chrome.save_frame_as_pdf("Content", f"/tmp/HON/{vin}.pdf")

        #self.chrome.save_frame_as_pdf("Content", f"C:\\Users\\<USER>\\Downloads\\Archive\\HON\\{vin}.pdf")

        time.sleep(2)

        self.chrome.switch_frame("Content")
       
        self.chrome.click_button('XPATH', '//*[@id="RDAAACKL_VI01_div"]/div[1]/input')
        self.utilities.sleep(1)

        self.chrome.click_button('XPATH', '/html/body/form/div[1]/table[3]/tbody/tr/td/input')
        self.utilities.sleep(1)

        self.chrome.switch_to_default()
        return response

    def navigate_to_search(self, entry=1):
        """
        Navigates to the search page.
        This example uses navigation via the Business Office menu.
        Adjust the selectors as needed.
        """

        self.stage = 'NAVIGATE_TO_SEARCH'
        try:
            self._log_info('Navigating to search page...')
            self.chrome.switch_to_default()
            if not self.chrome.click_button('XPATH', '//td[contains(normalize-space(string(.)), "BUSINESS") and contains(normalize-space(string(.)), "OFFICE")]'):
                raise Exception("Could not navigate to Business Office.")
            self.utilities.sleep(2)
            if not self.chrome.click_button('XPATH', '//table[.//td[normalize-space(text())="Acknowledgements"]]'):
                raise Exception("Could not navigate to Acknowledgements")
            self.utilities.sleep(2)
            self.chrome.switch_to_default()
            self._log_info('Navigation to search page succeeded.')
            return
        except Exception as e:
            self._handle_error(e)
            raise e
        
    def iterate_vins(self, iterator=1):
        """Iterate over VINs"""
        self.stage = 'ITERATE_VINS'
        try:
            self.chrome.switch_frame("Content")
            table = self.chrome.get_element('CSS_SELECTOR', '#rdaaack-VI01-table2')
            if not table:
                self._log_info('No table found. Skipping...')
                return
            # Retrieve all rows
            rows = self.chrome.get_elements('CSS_SELECTOR', '#rdaaack-VI01-table2 > tbody > tr')
            if not rows:
                return
            
            if iterator >= len(rows):
                self._log_info('All VINs processed!')
                return
            
            # Store VIN list in first iteration
            if iterator == 1:
                # Skip first row (header)
                self._log_info(f'Rows found: {len(rows)-1}')
                self.vin_list = [self.chrome.get_table_elements(rows[i + 1], 'TAG', 'td')[3].text.strip() for i in range(len(rows) - 1)]
                self._log_info(f'VIN list: {self.vin_list}')
                
            cells = self.chrome.get_table_elements(rows[iterator], 'TAG', 'td')
            try:
                self.vin = cells[3].text.strip()
                # link = self.chrome.get_second_element(cells[2], 'TAG', 'a')
                # link_href = link.get_attribute('href')
            except Exception as e:
                self._log_error(f'Error retrieving VIN: {e}')
                self.iterate_vins(iterator + 1)
                return
            
            try:
                stored = self.download_vin(iterator)
            except Exception as e:
                self._log_error(f'Could not download VIN {self.vin}: {e}')
                stored = False
            if stored:
                self._log_info(f'VIN {self.vin} downloaded!')
                self.tracker[self.vin] = {'error': None, 'stored': True, 'stage': self.stage}
            else:
                self.tracker[self.vin] = {'error': 'Could not download invoice', 'stored': False, 'stage': self.stage}

            # Iterate next row
            self.iterate_vins(iterator + 1)
            return
            
        except Exception as e:
            self._handle_error(e)


    def download_vin(self, iteration=1):
        """
        Searches for an invoice by VIN:
          - Selects the VIN search mode.
          - Enters the VIN and refines it by using its last eight digits.
          - Selects the proper brand from the dropdown.
          - Opens the invoice; if needed, handles a secondary login.
        """
        self.stage = 'SEARCH_INVOICE'
        # Try to navigate to the invoice page
        try:
            self.chrome.switch_frame("Content")

            # Click on invoice for the VIN
            self._log_info("Navigating to invoice details...")
            xpath = f'//table[@id="rdaaack-VI01-table2"]//tr[td[text()="{self.vin}"]]//a[@id="AckRef"]'
            if not self.chrome.click_button('XPATH', xpath):
                raise Exception("Could not click on invoice details")
        except Exception as e:
            self._handle_error(e)
            raise e

        # Donwload the invoice
        try:
            self.utilities.sleep(4)
            
            directory = f'/tmp/{self.store}/'
            frame_html = self.chrome.save_frame_as_pdf('Content', f'{directory}{self.vin}.pdf')
            file_path = f'{directory}{self.vin}.txt'
            with open(f'{file_path}', "w", encoding="utf-8") as f:
                f.write(frame_html)
            self._log_info(f"PDF saved to {file_path}.pdf")
            if not self.upload_to_s3(file_path, s3_path=self.s3_path, filename=self.vin, extension=".pdf"):
                raise Exception("Could not upload PDF to S3")
            
            return True
        except Exception as e:
            self._handle_error(e)
            raise e
        finally:
            # Go back to the search page
            self._log_info("Navigating back to search page...")
            self.chrome.back_arrow()
            self.utilities.sleep(1)
            self.chrome.switch_to_default()

    def get_pricing_sheet(self):
        """
        Retrieves the pricing sheet:
          - Navigates to Business Office > Invoices and Statements > Vehicle Pricing.
          - Selects the "Full Pricing and Allowance Schedule" link.
          - Initiates the PDF download.
        """
        self.stage = 'PRICING_SHEET'
        try:
            self._log_info("Initiating PDF download for pricing sheet...")
            self.chrome.switch_to_default()
            if not self.chrome.click_button('XPATH', '//td[contains(normalize-space(string(.)), "BUSINESS") and contains(normalize-space(string(.)), "OFFICE")]'):
                raise Exception("Could not navigate to Business Office.")
            self.utilities.sleep(2)
            if not self.chrome.click_button('XPATH', '//table[.//td[text()="AHM Invoices and Statements"]]'):
                raise Exception("Could not navigate to AHM Invoices and Statements.")
            self.utilities.sleep(2)
            self.chrome.switch_frame("Content")
            if not self.chrome.click_button('XPATH', '//a[text()="Vehicle Pricing"]'):
                raise Exception("Could not click on Vehicle Pricing")
            self.utilities.sleep(2)
            if not self.chrome.click_button('XPATH', '//a[contains(@href, "RedirectVehicleFullPricing")]'):
                raise Exception("Could not click on Full Pricing & Allowance Schedule")
            self.utilities.sleep(2)
            if not self.chrome.click_button('XPATH', '//a[button[@id="open-button"]]'):
                raise Exception("Could not click on Open button")
            
            self.utilities.sleep(1)
            today_path = self.utilities.get_today('%Y/%m/%d')
            self.get_file_from_download(
                extension='.pdf',
                name=f'{self.brand}_{self.store}_pricing_sheet',
                s3_path=f'invoices_information/{today_path}/',
                stage='PRICING_SHEET'
            )
            self.chrome.switch_to_default()
            self.utilities.sleep(5)
            return True
        except Exception as e:
            self._handle_error(e)
            raise e
 
    def save_invoice(self):
        """Downloads and renames the invoice based on the VIN."""
        try:
            self.utilities.sleep(4)  # Allow navigation delay
            self.get_file_from_download()
        except Exception as e:
            self._handle_error(e)