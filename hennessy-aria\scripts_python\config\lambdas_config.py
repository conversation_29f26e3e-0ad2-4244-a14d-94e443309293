def get_lambda_config(env):

    security_group_ids_snd = ['sg-0073e23cc9281b4f2', 'sg-0b48d49b8580b9bee']
    subnets_ids_snd = ['subnet-08d9a7a9cc266212d', 'subnet-03a91c27ea653680b']

    security_group_ids_prd = ['sg-0e34958ac5ac835e6', 'sg-0e74b20a104887796']
    subnets_ids_prd = ['subnet-097891c443a14b42d', 'subnet-0d8de56bd8f7144fc']

    security_group_ids = security_group_ids_snd if env == "snd-hen" else security_group_ids_prd
    subnets_ids = subnets_ids_snd if env == "snd-hen" else subnets_ids_prd

    return {
        "lambda_authorizer": {
            'function_name': f'{env}-lambda_authorizer',
            'role_name': 'lambda_authorizer',
            'description': 'Authorizer',
            'zip_file_path': 'artifacts/lambda_authorizer.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env
                },
                "prd-hen": {
                    'ENV': env
                },
            },
            'timeout': 180,
            'memory_size': 128,
            'layers': []
        },


        "bre_handler":{
            'function_name': f'{env}-bre_handler',
            'role_name': 'bre_handler',
            'description': 'Bre handler',
            'zip_file_path': 'artifacts/bre_handler.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env,
                    "MONGO_DATABASE": "snd_hennessy",
                    "BRE_LAMBDA": f"{env}-bre",
                    "BRE_LAMBDA_PRE_INVENTORY": f"{env}-bre_pre_inventory",
                    "BRE_LAMBDA_USED_CARS": f"{env}-bre_used_cars",
                    "LLM_LAMBDA": f"{env}-llm_extractor",
                    "LLM_LAMBDA_PRE_INVENTORY": f"{env}-llm_extractor_pre_inventory",
                    "LLM_LAMBDA_USED_CARS": f"{env}-llm_extractor_used_cars",
                    "ARIA_APP_ID_POST_INVENTORY": "73108b73-7960-4039-b5bf-bbc11cc606be",
                    "ARIA_APP_ID_BOLS": "4ab7fb3c-2247-459e-85b5-cdb6dc9c19fe",
                    "ARIA_APP_ID_TITLES": "09ccde48-4ed6-46e0-8ab4-174ca0484919",
                    "ARIA_APP_ID_PRE_INVENTORY": "19f3e0d3-b404-4bd5-99e9-93572e9c308e",
                    "ARIA_APP_ID_USED_CARS": "9459a3e3-dcee-43ca-b46a-71857d605ba6",
                    "ARIA_ENV": "sandbox"
                },
                "prd-hen": {
                    'ENV': env,
                    "MONGO_DATABASE": "prd_hennessy",
                    "BRE_LAMBDA": f"{env}-bre",
                    "BRE_LAMBDA_PRE_INVENTORY": f"{env}-bre_pre_inventory",
                    "BRE_LAMBDA_USED_CARS": f"{env}-bre_used_cars",
                    "LLM_LAMBDA": f"{env}-llm_extractor",
                    "LLM_LAMBDA_PRE_INVENTORY": f"{env}-llm_extractor_pre_inventory",
                    "LLM_LAMBDA_USED_CARS": f"{env}-llm_extractor_used_cars",
                    "ARIA_APP_ID_POST_INVENTORY": "234617d1-59bd-4b2f-b9b4-4ad5de283dd8",
                    "ARIA_APP_ID_BOLS": "49b382f1-9de3-4372-9388-2e34b5d5c9f7",
                    "ARIA_APP_ID_TITLES": "caa3d93e-9e96-4549-97a0-a241015b3dbd",
                    "ARIA_APP_ID_PRE_INVENTORY": "",
                    "ARIA_APP_ID_USED_CARS": "6a371f66-995c-43ce-a168-05849d04ff7e",
                    "ARIA_ENV": "ariahennessy"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ["pymongoAWS_2", "requests_2"]
        },


        "bre":{
            'function_name': f'{env}-bre',
            'role_name': 'bre',
            'description': 'Bre for post inventory',
            'zip_file_path': 'artifacts/bre.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env, 
                    'MONGO_DATABASE': 'snd_hennessy', 
                    'DEFAULT_BRE_TYPE': 'required_fields_and_calculations', 
                    'LLM_MESSENGER_LAMBDA': f'{env}-llm_messenger', 
                    'ARIA_ENV': 'sandbox'
                },
                "prd-hen": {
                    'ENV': env, 
                    'MONGO_DATABASE': 'prd_hennessy', 
                    'DEFAULT_BRE_TYPE': 'required_fields_and_calculations', 
                    'LLM_MESSENGER_LAMBDA': f'{env}-llm_messenger', 
                    'ARIA_ENV': 'ariahennesy'
                }
            },
            'timeout': 900,
            'memory_size': 2048,
            'layers': ['pymongoAWS_2', 'requests_2', 'vininfo_2']
        },


        "bre_used_cars": {
            'function_name': f'{env}-bre_used_cars',
            'role_name': 'bre_used_cars',
            'description': 'Bre for used cars inventory',
            'zip_file_path': 'artifacts/bre_used_cars.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env, 
                    'MONGO_DATABASE': 'snd_hennessy', 
                    'DEFAULT_BRE_TYPE': 'required_fields_and_calculations', 
                    'LLM_MESSENGER_LAMBDA': f'{env}-llm_messenger', 
                    'ARIA_ENV': 'sandbox',
                    'DOWC_AMT': '300.0',
                    'APEX_AMT': '296.0'
                },
                "prd-hen": {
                    'ENV': env, 
                    'MONGO_DATABASE': 'prd_hennessy', 
                    'DEFAULT_BRE_TYPE': 'required_fields_and_calculations', 
                    'LLM_MESSENGER_LAMBDA': f'{env}-llm_messenger', 
                    'ARIA_ENV': 'ariahennesy',
                    'DOWC_AMT': '300.0',
                    'APEX_AMT': '296.0'
                }
            },
            'timeout': 900,
            'memory_size': 2048,
            'layers': ['pymongoAWS_2', 'requests_2', 'vininfo_2']
        },

        "bre_pre_inventory": {
            'function_name': f'{env}-bre_pre_inventory',
            'role_name': 'bre_pre_inventory',
            'description': 'Bre for pre inventory',
            'zip_file_path': 'artifacts/bre_pre_inventory.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env, 
                    'MONGO_DATABASE': 'snd_hennessy', 
                    'DEFAULT_BRE_TYPE': 'required_fields_and_calculations', 
                    'LLM_MESSENGER_LAMBDA': f'{env}-llm_messenger', 
                    'ARIA_ENV': 'sandbox'
                },
                "prd-hen": {
                    'ENV': env, 
                    'MONGO_DATABASE': 'prd_hennessy', 
                    'DEFAULT_BRE_TYPE': 'required_fields_and_calculations', 
                    'LLM_MESSENGER_LAMBDA': f'{env}-llm_messenger', 
                    'ARIA_ENV': 'ariahennesy'
                }
            },
            'timeout': 900,
            'memory_size': 2048,
            'layers': ['pymongoAWS_2', 'requests_2', 'vininfo_2']
        },

        "llm_messenger": {
            'function_name': f'{env}-llm_messenger',
            'role_name': 'llm_messenger',
            'description': 'Llm messenger',
            'zip_file_path': 'artifacts/llm_messenger.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env, 
                    'SECRET_LLM_CREDENTIALS': f'{env}-llm_params'
                },
                "prd-hen": {
                    'ENV': env, 
                    'SECRET_LLM_CREDENTIALS': f'{env}-llm_params'
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['open_ai_2', 'requests_2', 'pydantic_2']
        },


        "pdf_utils": {
            'function_name': f'{env}-pdf_utils',
            'role_name': 'pdf_utils',
            'description': '',
            'zip_file_path': 'artifacts/pdf_utils.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.12',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env, 
                    'BUCKET': f'{env}-bucket'
                },
                "prd-hen": {
                    'ENV': env, 
                    'BUCKET': f'{env}-bucket'
                }
            },
            'timeout': 900,
            'memory_size': 2048,
            'layers': ['pymupdf_2', 'reportlab_2']
        },


        "email_watcher": {
            'function_name': f'{env}-email_watcher',
            'role_name': 'email_watcher',
            'description': '',
            'zip_file_path': 'artifacts/email_watcher.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env, 
                    'BUCKET': f'{env}-bucket',
                    "FILTER_VALUE": "",
                    "MONGO_DATABASE": "snd_hennessy",
                    "MOVE_EMAILS": "false",
                    "MOVE_EMAIL_LAMBDA": f"{env}-move_email",
                    "PROCESSED_FOLDER": "Processing",
                    "PROCESS_EMAIL_LAMBDA": f"{env}-process_email",
                    "REPORT_EMAIL_USERS": "<EMAIL>,<EMAIL>",
                    "SOURCE_FOLDER": "inbox",
                    "TOP_FETCH": "50",
                    "UNSORTED_FOLDER": "04-Unsorted"
                },
                "prd-hen": {
                    'ENV': env, 
                    'BUCKET': f'{env}-bucket',
                    "FILTER_VALUE": "",
                    "MONGO_DATABASE": "prd_hennessy",
                    "MOVE_EMAILS": "false",
                    "MOVE_EMAIL_LAMBDA": f"{env}-move_email",
                    "PROCESSED_FOLDER": "Processing",
                    "PROCESS_EMAIL_LAMBDA": f"{env}-process_email",
                    "REPORT_EMAIL_USERS": "<EMAIL>,<EMAIL>",
                    "SOURCE_FOLDER": "inbox",
                    "TOP_FETCH": "50",
                    "UNSORTED_FOLDER": "04-Unsorted"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['msal_2', 'pymongoAWS_2', 'requests_2']
        },



        "movel_email": {
            'function_name': f'{env}-move_email',
            'role_name': 'move_email',
            'description': '',
            'zip_file_path': 'artifacts/move_email.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env, 
                    "MOVE_EMAILS": "false",
                },
                "prd-hen": {
                    'ENV': env, 
                    "MOVE_EMAILS": "true",
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['msal_2', 'requests_2']
        },


        "process_email": {
            'function_name': f'{env}-process_email',
            'role_name': 'process_email',
            'description': '',
            'zip_file_path': 'artifacts/process_email.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "ARIA_APP_ID": "4ab7fb3c-2247-459e-85b5-cdb6dc9c19fe",
                    "ARIA_APP_ID_USED_CARS": "9459a3e3-dcee-43ca-b46a-71857d605ba6",
                    "ARIA_ENV": "sandbox",
                    "ENV": f"{env}",
                    "BUCKET": f"{env}-bucket",
                    "FILTER_VALUE": "",
                    "GROUP_NAME": "bol",
                    "GROUP_NAME_USED_CARS": "used_cars_invoices",
                    "MONGO_DATABASE": "snd_hennessy",
                    "MOVE_EMAILS": "false",
                    "MOVE_EMAIL_LAMBDA": f"{env}-move_email",
                    "PROCESSED_FOLDER": "Processing",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils",
                    "LLM_MESSENGER_LAMBDA": f"{env}-llm_messenger",
                    "PROCESS_EMAIL_LAMBDA": f"{env}-process_email",
                    "SOURCE_FOLDER": "act_test_copy_dont_delete",
                    "UNSORTED_FOLDER": "04-Unsorted",
                    "ATTACHMENTS_FOLDER": "attachments_files"
                },
                "prd-hen": {
                    "ARIA_APP_ID": "4ab7fb3c-2247-459e-85b5-cdb6dc9c19fe",
                    "ARIA_APP_ID_USED_CARS": "6a371f66-995c-43ce-a168-05849d04ff7e",
                    "ARIA_ENV": "ariahennesy",
                    "ENV": f"{env}",
                    "BUCKET": f"{env}-bucket",
                    "FILTER_VALUE": "",
                    "GROUP_NAME": "bol",
                    "GROUP_NAME_USED_CARS": "used_cars_invoices",
                    "MONGO_DATABASE": "prd_hennessy",
                    "MOVE_EMAILS": "false",
                    "MOVE_EMAIL_LAMBDA": f"{env}-move_email",
                    "PROCESSED_FOLDER": "Processing",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils",
                    "LLM_MESSENGER_LAMBDA": f"{env}-llm_messenger",
                    "PROCESS_EMAIL_LAMBDA": f"{env}-process_email",
                    "SOURCE_FOLDER": "act_test_copy_dont_delete",
                    "UNSORTED_FOLDER": "04-Unsorted",
                    "ATTACHMENTS_FOLDER": "attachments_files"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['msal_2', 'requests_2', "pymongoAWS_2", 'PyPDF2_2']
        },


        "llm_extractor": {
            'function_name': f'{env}-llm_extractor',
            'role_name': 'llm_extractor',
            'description': '',
            'zip_file_path': 'artifacts/llm_extractor.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.10',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "BRE_LAMBDA": f"{env}-bre",
                    "ENV": f"{env}",
                    "LLM_EXTRACTOR_COLLECTION_NAME": "llm_extractor",
                    "MONGO_DATABASE": "snd_hennessy",
                    "aria_env": "sandbox",
                    "RETRYS": "3",
                },
                "prd-hen": {
                    "BRE_LAMBDA": f"{env}-bre",
                    "ENV": f"{env}",
                    "LLM_EXTRACTOR_COLLECTION_NAME": "llm_extractor",
                    "MONGO_DATABASE": "prd_hennessy",
                    "aria_env": "ariahennesy",
                    "RETRYS": "3"
                }
            },
            'timeout': 900,
            'memory_size': 2000,
            'layers': ['open_ai_2', 'requests_2', 'pymongoAWS_2']
        },


        "llm_extractor_used_cars":{
            'function_name': f'{env}-llm_extractor_used_cars',
            'role_name': 'llm_extractor_used_cars',
            'description': '',
            'zip_file_path': 'artifacts/llm_extractor_used_cars.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.10',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "BRE_LAMBDA_USED_CARS": f"{env}-bre_used_cars",
                    "ENV": f"{env}",
                    "LLM_EXTRACTOR_COLLECTION_NAME": "llm_extractor",
                    "MONGO_DATABASE": "snd_hennessy",
                    "aria_env": "sandbox",
                    "RETRYS": "3",
                },
                "prd-hen": {
                    "BRE_LAMBDA_USED_CARS": f"{env}-bre_used_cars",
                    "ENV": f"{env}",
                    "LLM_EXTRACTOR_COLLECTION_NAME": "llm_extractor",
                    "MONGO_DATABASE": "prd_hennessy",
                    "aria_env": "ariahennesy",
                    "RETRYS": "3"
                }
            },
            'timeout': 900,
            'memory_size': 2000,
            'layers': ['open_ai_2', 'requests_2', 'pymongoAWS_2']
        },


        "llm_extractor_pre_inventory":{
            'function_name': f'{env}-llm_extractor_pre_inventory',
            'role_name': 'llm_extractor_pre_inventory',
            'description': '',
            'zip_file_path': 'artifacts/llm_extractor_pre_inventory.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.10',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "BRE_LAMBDA_PRE_INVENTORY": f"{env}-bre_pre_inventory",
                    "ENV": f"{env}",
                    "LLM_EXTRACTOR_COLLECTION_NAME": "llm_extractor",
                    "MONGO_DATABASE": "snd_hennessy",
                    "aria_env": "sandbox",
                    "RETRYS": "3",
                },
                "prd-hen": {
                    "BRE_LAMBDA_PRE_INVENTORY": f"{env}-bre_pre_inventory",
                    "ENV": f"{env}",
                    "LLM_EXTRACTOR_COLLECTION_NAME": "llm_extractor",
                    "MONGO_DATABASE": "prd_hennessy",
                    "aria_env": "ariahennesy",
                    "RETRYS": "3"
                }
            },
            'timeout': 900,
            'memory_size': 2000,
            'layers': ['open_ai_2', 'requests_2', 'pymongoAWS_2']
        },


        "report_sftp_to_s3": {
            'function_name': f'{env}-report_sftp_to_s3',
            'role_name': 'report_sftp_to_s3',
            'description': '',
            'zip_file_path': 'artifacts/reynols_report_processor.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "snd_hennessy",
                    "REYNOLS_REPORT_FOLDER": "reynols_reports",
                    "SFTP_CREDENTIALS": f"{env}-sftp_credentials",
                    "SFTP_FILES_PATH": "/invokescans/reports",
                    "FILE_SFTP_EXTENSION": ".csv",
                    "PRE_INVENTORY_REPORT_FOLDER": "",
                    "SFTP_FILES_PATH_PRE_INVENTORY": "",
                    "USED_CARS_REPORT_FOLDER": "used_cars_reports",
                    "SFTP_FILES_PATH_USED_CARS": "/invokescans/reports"
                },
                "prd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "prd_hennessy",
                    "REYNOLS_REPORT_FOLDER": "reynols_reports",
                    "SFTP_CREDENTIALS": f"{env}-sftp_credentials",
                    "SFTP_FILES_PATH": "/invokescans/reports",
                    "FILE_SFTP_EXTENSION": ".csv",
                    "PRE_INVENTORY_REPORT_FOLDER": "",
                    "SFTP_FILES_PATH_PRE_INVENTORY": "",
                    "USED_CARS_REPORT_FOLDER": "used_cars_reports",
                    "SFTP_FILES_PATH_USED_CARS": "/invokescans/reports"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['vininfo_2', 'pandas_2', 'paramiko_2', 'pymongoAWS_2']
        },


        "invoice_downloader": {
            'function_name': f'{env}-invoice_downloader',
            'role_name': 'invoice_downloader',
            'description': '',
            'zip_file_path': 'artifacts/invoice_downloader.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.12',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "snd_hennessy",
                    "INVOICES_FOLDER": "invoices",
                    "INVOICES_FOLDER_PRE_INVENTORY": "invoices_new_cars",
                    "INVOICES_FOLDER_USED_CARS": "invoices_used_cars"
                },
                "prd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "prd_hennessy",
                    "INVOICES_FOLDER": "invoices",
                    "INVOICES_FOLDER_PRE_INVENTORY": "invoices_new_cars",
                    "INVOICES_FOLDER_USED_CARS": "invoices_used_cars"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['pymongoAWS_2']
        },


        "invoice_to_aria": {
            'function_name': f'{env}-invoice_to_aria',
            'role_name': 'invoice_to_aria',
            'description': '',
            'zip_file_path': 'artifacts/invoice_to_aria.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.12',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ARIA_ENV": "sandbox",
                    "GROUP_NAME_POST_INVENTORY": "invoice",
                    "ARIA_APP_ID_POST_INVENTORY": "73108b73-7960-4039-b5bf-bbc11cc606be",
                    "GROUP_NAME_PRE_INVENTORY": "invoices_pre_inventory",
                    "ARIA_APP_ID_PRE_INVENTORY": "19f3e0d3-b404-4bd5-99e9-93572e9c308e",
                    "GROUP_NAME_USED_CARS": "used_cars_invoices",
                    "ARIA_APP_ID_USED_CARS": "9459a3e3-dcee-43ca-b46a-71857d605ba6",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "snd_hennessy",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils"
                },
                "prd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ARIA_ENV": "ariahennesy",
                    "GROUP_NAME_POST_INVENTORY": "invoice",
                    "ARIA_APP_ID_POST_INVENTORY": "234617d1-59bd-4b2f-b9b4-4ad5de283dd8",
                    "GROUP_NAME_PRE_INVENTORY": "",
                    "ARIA_APP_ID_PRE_INVENTORY": "",
                    "GROUP_NAME_USED_CARS": "used_cars_invoices",
                    "ARIA_APP_ID_USED_CARS": "6a371f66-995c-43ce-a168-05849d04ff7e",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "prd_hennessy",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['pymongoAWS_2', 'requests_2']
        },


        "title_sftp_to_s3_to_aria": {
            'function_name': f'{env}-title_sftp_to_s3_to_aria',
            'role_name': 'title_sftp_to_s3_to_aria',
            'description': '',
            'zip_file_path': 'artifacts/title_sftp_to_s3_to_aria.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ARIA_ENV": "sandbox",
                    "GROUP_NAME": "title",
                    "ARIA_APP_ID": "09ccde48-4ed6-46e0-8ab4-174ca0484919",
                    "COMPLETE_TITLE_FOLDER": "titles",
                    "ENV": f"{env}",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils",
                    "LLM_MESSENGER_LAMBDA": f"{env}-llm_messenger",
                    "SFTP_FILES_PATH": "/invokescans/Titles_test",
                    "FILE_SFTP_EXTENSION": ".pdf",
                    "MONGO_DATABASE": "snd_hennessy",
                    "SFTP_CREDENTIALS": f"{env}-sftp_credentials"
                },
                "prd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ARIA_ENV": "ariahennesy",
                    "GROUP_NAME": "title",
                    "ARIA_APP_ID": "caa3d93e-9e96-4549-97a0-a241015b3dbd",
                    "COMPLETE_TITLE_FOLDER": "titles",
                    "ENV": f"{env}",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils",
                    "LLM_MESSENGER_LAMBDA": f"{env}-llm_messenger",
                    "SFTP_FILES_PATH": "/invokescans",
                    "FILE_SFTP_EXTENSION": ".pdf",
                    "MONGO_DATABASE": "prd_hennessy",
                    "SFTP_CREDENTIALS": f"{env}-sftp_credentials"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['requests_2', 'pymongoAWS_2', 'paramiko_2', 'pymupdf_2']
        },



        "orchestrator_downloader": {
            'function_name': f'{env}-orchestrator_downloader',
            'role_name': 'orchestrator_downloader',
            'description': 'Orchestrator',
            'zip_file_path': 'artifacts/orchestrator_downloader.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.12',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env, 
                    'BUCKET': f'{env}-bucket', 
                    'MONGO_DATABASE': 'snd_hennessy', 
                    'ARIA_ENV': 'sandbox'
                },
                "prd-hen": {
                    'ENV': env, 
                    'BUCKET': f'{env}-bucket',
                    'MONGO_DATABASE': 'prd_hennessy',
                    'ARIA_ENV': 'ariahennesy'
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['pymongoAWS_2', 'requests_2']
        },


        "reconciliate": {
            'function_name': f'{env}-reconciliate',
            'role_name': 'reconciliate',
            'description': '',
            'zip_file_path': 'artifacts/reconciliate.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "snd_hennessy",
                    "ARIA_ENV": "sandbox",
                    "BRE_HANDLER_LAMBDA": f"{env}-bre_handler",
                    "BUCKET": f"{env}-bucket",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils",
                    "REPORT_TO_ARIA_LAMBDA": f"{env}-report_to_aria"
                },
                "prd-hen": {
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "prd_hennessy",
                    "ARIA_ENV": "ariahennesy",
                    "BRE_HANDLER_LAMBDA": f"{env}-bre_handler",
                    "BUCKET": f"{env}-bucket",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils",
                    "REPORT_TO_ARIA_LAMBDA": f"{env}-report_to_aria"
                }   
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['pymongoAWS_2', 'requests_2', 'pydantic_2']
        },


        "orchestrator_download_update":  {
            'function_name': f'{env}-orchestrator_download_update',
            'role_name': 'orchestrator_download_update',
            'description': '',
            'zip_file_path': 'artifacts/orchestrator_download_update.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": { 
                    "BUCKET": f"{env}-bucket",
                    "ARIA_ENV": "sandbox",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "snd_hennessy",
                    "REPORT_EMAIL": "<EMAIL>",
                    "SUPPORT_EMAIL": "<EMAIL>",
                    "ARIA_EMAIL": "<EMAIL>",
                    "REPORT_TO_ARIA_LAMBDA": f"{env}-report_to_aria"
                },
                "prd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ARIA_ENV": "ariahennesy",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "prd_hennessy",
                    "REPORT_EMAIL": "<EMAIL>,<EMAIL>,<EMAIL>",
                    "SUPPORT_EMAIL": "<EMAIL>,<EMAIL>,<EMAIL>",
                    "ARIA_EMAIL": "<EMAIL>",
                    "REPORT_TO_ARIA_LAMBDA": f"{env}-report_to_aria"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['pymongoAWS_2', 'requests_2', 'msal_2', 'pandas_2']
        },

        
        "reevaluate":  {
            'function_name': f'{env}-reevaluate',
            'role_name': 'reevaluate',
            'description': '',
            'zip_file_path': 'artifacts/reevaluate.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "ARIA_ENV": "sandbox",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "snd_hennessy",
                    "BRE_HANDLER_LAMBDA": f"{env}-bre_handler",
                    "BUCKET": f"{env}-bucket",
                },
                "prd-hen": {
                    "ARIA_ENV": "ariahennesy",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "prd_hennessy",
                    "BRE_HANDLER_LAMBDA": f"{env}-bre_handler",
                    "BUCKET": f"{env}-bucket",
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['pymongoAWS_2', 'requests_2', 'pydantic_2']
        },



        "python_handler": {
            'function_name': f'{env}-python_handler',
            'role_name': 'python_handler',
            'description': '',
            'zip_file_path': 'artifacts/python_handler.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "ENV": f"{env}"
                },
                "prd-hen": {
                    "ENV": f"{env}"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': []
        },


        "loading_wi_report": {
            'function_name': f'{env}-loading_wi_report',
            'role_name': 'loading_wi_report',
            'description': '',
            'zip_file_path': 'artifacts/loading_wi_report.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "ENV": f"{env}",
                    "ARIA_ENV": "sandbox",
                    "REPORTER_EMAIL": "<EMAIL>",
                    "REPORTS_EMAIL": "<EMAIL>",#<EMAIL>,
                    "BCC_EMAIL": "<EMAIL>,<EMAIL>"
                },
                "prd-hen": {
                    "ENV": f"{env}",
                    "ARIA_ENV": "ariahennesy",
                    "REPORTER_EMAIL": "<EMAIL>",
                    "REPORTS_EMAIL": "<EMAIL>",#<EMAIL>,
                    "BCC_EMAIL": "<EMAIL>,<EMAIL>"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ["requests_2", "msal_2", "pydantic_2"]
        },



        "error_wi_report": {
            'function_name': f'{env}-error_wi_report',
            'role_name': 'error_wi_report',
            'description': '',
            'zip_file_path': 'artifacts/error_wi_report.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "ENV": f"{env}",
                    "ARIA_ENV": "sandbox",
                    "REPORTER_EMAIL": "<EMAIL>",
                    "REPORTS_EMAIL": "<EMAIL>",
                    "BCC_EMAIL": "<EMAIL>,<EMAIL>",
                    "MONGO_DATABASE": "snd_hennessy",
                    "RECEIVER_KEY": f"{env}_invoke"
                },
                "prd-hen": {
                    "ENV": f"{env}",
                    "ARIA_ENV": "ariahennesy",
                    "REPORTER_EMAIL": "<EMAIL>",
                    "REPORTS_EMAIL": "<EMAIL>",
                    "BCC_EMAIL": "<EMAIL>,<EMAIL>",
                    "MONGO_DATABASE": "prd_hennessy",
                    "RECEIVER_KEY": f"{env}_invoke"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ["requests_2", "msal_2", "pydantic_2", "pymongoAWS_2"]
        },

        "report_loaded_data": {
            'function_name': f'{env}-report_loaded_data',
            'role_name': 'report_loaded_data',
            'description': '',
            'zip_file_path': 'artifacts/report_loaded_data.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "ENV": f"{env}",
                    "ARIA_ENV": "sandbox",
                    "REPORTER_EMAIL": "<EMAIL>",
                    "REPORTS_EMAIL": "<EMAIL>",
                    "BCC_EMAIL": "<EMAIL>,<EMAIL>",
                    "MONGO_DATABASE": "snd_hennessy",
                },
                "prd-hen": {
                    "ENV": f"{env}",
                    "ARIA_ENV": "ariahennesy",
                    "REPORTER_EMAIL": "<EMAIL>",
                    "REPORTS_EMAIL": "<EMAIL>",
                    "BCC_EMAIL": "<EMAIL>,<EMAIL>",
                    "MONGO_DATABASE": "prd_hennessy",
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ["jinja2_2", "msal_2", "pandas_2", "pymongoAWS_2"]
        },

        "queues_handler": {
            'function_name': f'{env}-queues_handler',
            'role_name': 'queues_handler',
            'description': '',
            'zip_file_path': 'artifacts/queues_handler.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "ENV": f"{env}",
                },
                "prd-hen": {
                    "ENV": f"{env}",
                }
            },
            'timeout': 180,
            'memory_size': 128,
            'layers': ["pymongoAWS_2"]
        },

        "secrets_handler": {
            'function_name': f'{env}-secrets_handler',
            'role_name': 'secrets_handler',
            'description': '',
            'zip_file_path': 'artifacts/secrets_handler.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "ENV": f"{env}",
                },
                "prd-hen": {
                    "ENV": f"{env}",
                }
            },
            'timeout': 180,
            'memory_size': 128,
            'layers': []
        },

        "report_pages_not_used_in_titles": {
            'function_name': f'{env}-report_pages_not_used_in_titles',
            'role_name': 'report_pages_not_used_in_titles',
            'description': '',
            'zip_file_path': 'artifacts/report_pages_not_used_in_titles.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "ENV": f"{env}",
                    "ARIA_ENV": "sandbox",
                    "REPORTER_EMAIL": "<EMAIL>",
                    "REPORTS_EMAIL": "<EMAIL>",
                    "BCC_EMAIL": "<EMAIL>,<EMAIL>",
                    "MONGO_DATABASE": "snd_hennessy"
                },
                "prd-hen": {
                    "ENV": f"{env}",
                    "ARIA_ENV": "ariahennesy",
                    "REPORTER_EMAIL": "<EMAIL>",
                    "REPORTS_EMAIL": "<EMAIL>",
                    "BCC_EMAIL": "<EMAIL>,<EMAIL>",
                    "MONGO_DATABASE": "prd_hennessy"
                }                
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ["pymongoAWS_2", "msal_2", "pandas_2", "jinja2_2"]
        },

        "report_to_aria": {
            'function_name': f'{env}-report_to_aria',
            'role_name': 'report_to_aria',
            'description': '',
            'zip_file_path': 'artifacts/report_to_aria.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ARIA_ENV": "sandbox",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "snd_hennessy",
                    "GROUP_NAME": "reports",
                    "ARIA_APP_ID": "cf44b991-1dda-439c-a9f8-bb17e02f8635",
                    "REYNOLS_REPORT_FOLDER": "reynols_reports",
                    "USED_CARS_REPORTS": "used_cars_reports"
                },
                "prd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ARIA_ENV": "ariahennesy",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "prd_hennessy",
                    "GROUP_NAME": "reports",
                    "ARIA_APP_ID": "10526188-7fdb-4c07-a9a9-6db3bf9bd2e7",
                    "REYNOLS_REPORT_FOLDER": "reynols_reports",
                    "USED_CARS_REPORTS": "used_cars_reports"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ["pymongoAWS_2", "requests_2", "pandas_2", "openpyxl_2"]
        },


        "load_pricing_guide": {
            'function_name': f'{env}-load_pricing_guide',
            'role_name': 'load_pricing_guide',
            'description': '',
            'zip_file_path': 'artifacts/load_pricing_guide.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "snd_hennessy",
                    "REYNOLS_REPORT_FOLDER": "reynols_reports",
                    "ARIA_ENV": "sandbox",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils",
                    "PRICING_GUIDE_FOLDER": "invoices_information"
                },
                "prd-hen": {    
                    "BUCKET": f"{env}-bucket",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "prd_hennessy",
                    "REYNOLS_REPORT_FOLDER": "reynols_reports",
                    "ARIA_ENV": "ariahennesy",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils",
                    "PRICING_GUIDE_FOLDER": "invoices_information"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ["pymongoAWS_2", "pymupdf_2"]
        },


        "load_pricing_guide_extractor": {
            'function_name': f'{env}-load_pricing_guide_extractor',
            'role_name': 'load_pricing_guide_extractor',
            'description': '',
            'zip_file_path': 'artifacts/load_pricing_guide_extractor.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "snd_hennessy",
                    "ARIA_ENV": "sandbox",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils",
                    "LLM_MESSENGER_LAMBDA": f"{env}-llm_messenger",
                    "PRICING_GUIDE_FOLDER": "invoices_information"
                },
                "prd-hen": {
                    "BUCKET": f"{env}-bucket",
                    "ENV": f"{env}",
                    "MONGO_DATABASE": "prd_hennessy",
                    "ARIA_ENV": "ariahennesy",
                    "PDF_PROCESSER_LAMBDA": f"{env}-pdf_utils",
                    "LLM_MESSENGER_LAMBDA": f"{env}-llm_messenger",
                    "BUCKET_FOLDER": "textract_operations"
                }
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ["pymongoAWS_2"]
        },

        "pre_stock_in_vins": {
            'function_name': f'{env}-pre_stock_in_vins',
            'role_name': 'pre_stock_in_vins',
            'description': 'Loads the reports downloaded for each vendor',
            'zip_file_path': 'artifacts/pre_stock_in_vins.zip',
            'handler': 'lambda_function.lambda_handler',
            'runtime': 'python3.11',
            'security_group_ids': security_group_ids,
            'subnet_ids': subnets_ids,
            'environment_variables': {
                "snd-hen": {
                    'ENV': env, 
                    'BUCKET': f'{env}-bucket', 
                    'MONGO_DATABASE': 'snd_hennessy', 
                    'NEW_VEHICLES_REPORTS_FOLDER': 'new_vehicles_reports',
                    "REPORT_TO_ARIA_LAMBDA": "",
                    'REPORT_TO_ARIA_LAMBDA': f"{env}-report_to_aria"
                },
                "prd-hen": {
                    'ENV': env, 
                    'BUCKET': f'{env}-bucket', 
                    'MONGO_DATABASE': 'prd_hennessy', 
                    'NEW_VEHICLES_REPORTS_FOLDER': 'new_vehicles_reports',
                    'REPORT_TO_ARIA_LAMBDA': f"{env}-report_to_aria"
                },
            },
            'timeout': 900,
            'memory_size': 256,
            'layers': ['pymongoAWS_2', 'pandas_2', 'lxml_2']
        }
        
    }
