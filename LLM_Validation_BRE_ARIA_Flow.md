# LLM Validation, BRE Processing, and ARIA Loading Flow

## Overview
This document outlines the complete flow from LLM data response validation through Business Rules Engine (BRE) processing to ARIA system loading in the Hennessy-ARIA system.

## Key Components and File Paths

### 1. LLM Response Validation
**Primary Files:**
- `hennessy-aria/src/llm_messenger/lambda_function.py` - Main LLM communication handler
- `hennessy-aria/src/bre/bre_files/rules.py` - Contains validation rules that use LLM responses
- `hennessy-aria/src/process_email/email_processer_utils/bol_processer.py` - BOL-specific LLM validation

### 2. BRE (Business Rules Engine) Processing
**Primary Files:**
- `hennessy-aria/src/bre/lambda_function.py` - Main BRE orchestrator
- `hennessy-aria/src/bre/post_bre/post_bre.py` - Post-BRE processing and validation
- `hennessy-aria/src/bre_handler/lambda_function.py` - BRE handler and dispatcher
- `hennessy-aria/src/bre_pre_inventory/bre_files/bre.py` - Pre-inventory BRE processing
- `hennessy-aria/src/bre_used_cars/bre_files/bre.py` - Used cars BRE processing

### 3. ARIA Integration
**Primary Files:**
- `hennessy-aria/src/bre/utils/aria_utils.py` - ARIA communication utilities
- `hennessy-aria/src/bre_pre_inventory/utils/aria_utils.py` - Pre-inventory ARIA utilities
- `hennessy-aria/src/loading_wi_report/aria_utils.py` - ARIA client for work item reporting

## Detailed Flow

### Phase 1: LLM Response Validation

#### 1.1 LLM Messenger Response Handling
**File:** `hennessy-aria/src/llm_messenger/lambda_function.py`
```python
# Basic LLM response structure validation
if response['statusCode'] == 200:
    # Process successful response
    return {"statusCode": 200, "body": json.dumps({"message": response})}
else:
    # Handle error response
    return {"statusCode": 500, "body": json.dumps({"message": f"Error from LLM"})}
```

#### 1.2 Rule-Based LLM Validation
**File:** `hennessy-aria/src/bre/bre_files/rules.py`

**Example - Rule 25 Address Verification:**
```python
def rule_25(parsed_fields, valid_address):
    # Extract address from parsed fields
    address_extracted = parsed_fields['address'].get('value') if parsed_fields.get('address', '') else ''
    
    # Send to LLM for validation
    msg_to_verify = construct_address_verifier_rule_25(address_extracted, valid_address)
    llm_lambda_response = trigger_lambda_response(os.environ['LLM_MESSENGER_LAMBDA'], msg_to_verify)
    
    # Validate LLM response
    if llm_lambda_response['statusCode'] != 200:
        return {"statusCode": 500, "body": json.dumps({"message": "Error response from llm."})}
    
    # Parse and validate LLM response content
    llm_lambda_response = json.loads(llm_lambda_response['body'])
    llm_lambda_response = llm_lambda_response['message']
    
    # Apply business logic based on LLM response
    if "none" in llm_lambda_response.lower():
        result['address'] = {'pass': False, 'value': address_extracted, 'display': True}
    else:
        result['address'] = {'pass': True, 'value': address_extracted, 'display': True}
```

#### 1.3 BOL Classification Validation
**File:** `hennessy-aria/src/process_email/email_processer_utils/bol_processer.py`
```python
# LLM response validation for BOL classification
llm_lambda_response = trigger_lambda_response(os.environ['LLM_MESSENGER_LAMBDA'], msg_to_clasify)

if llm_lambda_response['statusCode'] != 200:
    return {"statusCode": 500, "body": json.dumps({"message": "Error response from llm."})}

# Parse and validate response content
llm_lambda_response = json.loads(llm_lambda_response['body'])
llm_lambda_response = llm_lambda_response['message']

if "false" in llm_lambda_response.lower():
    print("Skipping attachment, classified as no bol")
    return False, ""
```

### Phase 2: BRE Processing and Validation

#### 2.1 BRE Orchestration
**File:** `hennessy-aria/src/bre/lambda_function.py`
```python
def lambda_handler(event, context):
    # Initialize BRE with validated data
    bre = Bre(event, mongo_uri)
    bre_response = bre.run()
    
    # Post-BRE processing with additional validation
    post_bre_processor = PostBREProcessor(bre_response, mongo_uri)
    response = post_bre_processor.execute()
    
    return response
```

#### 2.2 Rule Execution and Validation
**File:** `hennessy-aria/src/bre_pre_inventory/bre_files/bre.py`
```python
def execute_rules(self):
    # Execute rules and validate results
    execution_rules_output, passed_rules, not_passed_rules = self.execute_rules()
    
    # Update parsed fields with validated execution results
    self.update_parsed_fields_with_execution_rules_output(execution_rules_output, self.document_type)
    
    # Determine next status based on rule validation
    if self.bre_type[self.ocr_groups[0]] == os.environ['DEFAULT_BRE_TYPE']:
        next_status_id, next_status_label, aria_exception, note = self.process_default_bre()
    else:
        next_status_id, next_status_label, aria_exception, note = self.process_based_on_rules_bre()
```

#### 2.3 Post-BRE Validation
**File:** `hennessy-aria/src/bre/post_bre/post_bre.py`
```python
def process_based_on_rules_bre(self):
    # Validate if all rules passed
    if self.valid_rules == self.passed_rules[self.ocr_groups[0]]:
        aria_exception = ''
        next_status_id = self.action['target_status']
        next_status_label = self.status_info[next_status_id]['label']
    else:
        # Handle failed validation
        aria_exception = "One or more fields require human intervention"
        # Set appropriate status for manual review
```

### Phase 3: ARIA Loading and Integration

#### 3.1 ARIA Response Preparation
**File:** `hennessy-aria/src/bre/post_bre/post_bre.py`
```python
def execute(self):
    try:
        # Process validation results
        if self.bre_type[self.ocr_groups[0]] == os.environ['DEFAULT_BRE_TYPE']:
            next_status_id, next_status_label, aria_exception, note = self.process_default_bre()
        else:
            next_status_id, next_status_label, aria_exception, note = self.process_based_on_rules_bre()
        
        # Update document status based on validation
        if "invoice" in self.ocr_groups[0]:
            update_bre_results = self.update_invoice(next_status_label)
        if "bol" in self.ocr_groups[0]:
            update_bre_results = self.update_bol(next_status_label, aria_exception)
        
        # Send validated data to ARIA
        if not self.request_response:
            self.aria.bre_reply(
                app_id=self.app_id,
                item_id=self.document_id,
                bre_response={
                    "aria_status": {"value": next_status_id},
                    "aria_exception": {"value": aria_exception},
                    self.ocr_groups[0]: self.parsed_fields[self.ocr_groups[0]]
                })
```

#### 3.2 ARIA Communication
**File:** `hennessy-aria/src/bre/utils/aria_utils.py`
```python
class ARIA():
    def bre_reply(self, app_id, item_id, bre_response):
        url = f'{self.base_url}/public/v1/apps/{app_id}/case_management_middleware/work_items/{item_id}/bre'
        body = {
            "data": {
                "type": "workItem",
                "id": item_id,
                "attributes": {
                    "response": bre_response
                }
            }
        }
        self.request_to_aria('post', url, body)
```

## Validation Points Summary

1. **LLM Response Status Code Validation** - Ensures 200 status code
2. **LLM Response Content Validation** - Parses and validates JSON structure
3. **Business Rule Validation** - Applies domain-specific validation rules
4. **BRE Rule Execution Validation** - Validates rule pass/fail status
5. **Document Type Validation** - Ensures proper document classification
6. **Status Transition Validation** - Validates workflow state changes
7. **ARIA Integration Validation** - Ensures proper data format for ARIA

## Error Handling and Exception Flow

- **LLM Errors**: Return 500 status with error message
- **Validation Failures**: Set `aria_exception` and route to manual review
- **BRE Processing Errors**: Log errors and set appropriate status
- **ARIA Communication Errors**: Handle HTTP response codes and retry logic

This flow ensures data integrity and proper validation at each stage before loading into the ARIA system.
