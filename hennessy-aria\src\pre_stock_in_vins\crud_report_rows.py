import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudReynolsReport:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='vin'
        )
    
    def insert_row_pre_inventory(self, reynols_row, report_name):
        """
        This function inserts a reynols report row into the database.
        STORE	RECEIVED	STOCK #	MAKE	DESC	VIN	INV AMT	SLS COST	STK IN NOTES	SVC RO DATE	STAT-CODE	JRNL-PURCH-DATE-DR
        """
        reynols_row_to_db = {
            "vin": reynols_row['VIN'],
            "status": 0,
            "status_history": [0],
            "current_flow": "pre-inventory",
            "updated_at": datetime.datetime.now(),
            "read_at": datetime.datetime.now(),
            "flows": {
                "pre-inventory":{
                        "report-data": {                       
                            "store": reynols_row.get('STORE', ""),
                            "make": reynols_row.get('MAKE', ""),
                            "commission_number": reynols_row.get('COMMISSION NUMBER', ""),
                            "model_code": reynols_row.get('MODEL CODE', ""),
                            "model_description": reynols_row.get('MODEL DESCRIPTION', ""),
                            "color": reynols_row.get('COLOR', ""),
                            "first_read_from_file": report_name
                        },
                        "docs": {
                            "invoice": {
                                "downloaded_at": "",
                                "aria_data": {
                                
                                },
                                "fields": {

                                }
                            }
                        }
                },
                "post-inventory": {
                    "report-data": {},
                    "docs": {
                        "invoice": {
                            "downloaded_at": "",
                            "aria_data": {
                            
                            },
                            "fields": {

                            }
                        },
                        "bol": {},
                        "title": {}
                    }
                },
                "used-cars": {
                    "report-data": {},
                    "docs": {
                        "invoice": {
                            "downloaded_at": "",
                            "aria_data": {
                            
                            },
                            "fields": {

                            }
                        }
                    }
                }
            }
        }

        self.mongo.insert_one(reynols_row_to_db)
        return reynols_row_to_db

    def update_row_pre_inventory(self, reynols_row, report_name, vin):
        """
        This function inserts a reynols report row into the database.
        STORE	RECEIVED	STOCK #	MAKE	DESC	VIN	INV AMT	SLS COST	STK IN NOTES	SVC RO DATE	STAT-CODE	JRNL-PURCH-DATE-DR
        """
        
        reynols_row_to_db = {
            "updated_at": datetime.datetime.now(),
            "flows.pre-inventory.report-data.store": reynols_row['STORE'],
            "flows.pre-inventory.report-data.make": reynols_row['MAKE'],
            "flows.pre-inventory.report-data.commission_number": reynols_row['COMMISSION NUMBER'],
            "flows.pre-inventory.report-data.model_code": reynols_row['MODEL CODE'],
            "flows.pre-inventory.report-data.model_description": reynols_row['MODEL DESCRIPTION'],
            "flows.pre-inventory.report-data.color": reynols_row['COLOR'],
            "flows.pre-inventory.report-data.last_read_from_file": report_name
        }

        self.mongo.update_one({"vin": vin}, {"$set": reynols_row_to_db})
        return reynols_row_to_db
    
    def find_report_row_by_vin(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin}
        return self.mongo.find_one(query)


    def __del__(self):
        self.mongo.close_connection()
