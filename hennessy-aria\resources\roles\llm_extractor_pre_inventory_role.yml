Resources:
  LLMExtractorUsedCarsRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ${self:provider.stage}-llm_extractor_pre_inventory
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: HENLLMExtractorUsedCarsPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              # EC2 permissions (for VPC connectivity)
              - Effect: Allow
                Action:
                  - ec2:DescribeNetworkInterfaces
                  - ec2:CreateNetworkInterface
                  - ec2:DeleteNetworkInterface
                Resource: "*"
              # Logs permissions                
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - "arn:aws:logs:*:*:log-group:/aws/lambda/${self:provider.stage}-llm_extractor_pre_inventory:*"
              # Secrets Manager permissions
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:ListSecrets
                Resource: "*"
              # KMS permissions (for encrypted resources)
              - Effect: Allow
                Action:
                  - kms:DescribeKey
                  - kms:ListAliases
                  - kms:ListKeys
                Resource: "*"
              # Lambda permissions (if this Lambda interacts with other Lambdas)
              - Effect: Allow
                Action:
                  - lambda:InvokeFunctionUrl
                  - lambda:InvokeFunction
                  - lambda:InvokeAsync
                Resource:
                  - "arn:aws:lambda:*:*:function:*"
              # Bedrock permissions
              - Effect: Allow
                Action:
                  - bedrock:InvokeModel
                Resource: "*"              
              # CloudFormation permissions
              - Effect: Allow
                Action:
                  - cloudformation:DescribeStacks
                  - cloudformation:CreateChangeSet
                  - cloudformation:DescribeChangeSet
                  - cloudformation:ExecuteChangeSet
                Resource: "*"
              - Effect: "Allow"
                Action: 
                  - logs:*
                  - cloudwatch:GenerateQuery
                Resource: 
                  - "*"