service: hennesy

provider:
  name: aws
  deploymentBucket:
    name: ${self:custom.deploymentBucketNames.${self:provider.stage}}
  region: us-east-1
  profile: ${opt:profile, self:custom.profiles.${self:provider.stage, 'local'}}
  stage: ${opt:stage, 'local'}
  stackName: ${self:provider.stage}-hennessy
  tracing:
    apiGateway: true
  ecr:
   images:
     selenium-downloader-latest:
        uri: ${self:custom.ecrImageUris.${self:provider.stage}}
  httpApi:
    authorizers:
      lambda_authorizer:
          type: request
          functionName: lambda_authorizer
          identitySource: $request.header.Authorization
          resultTtlInSeconds: 300
          enableSimpleResponses: true
          payloadVersion: "2.0"

package:
  individually: true
  patterns:
    - "!**/*"
    - "layers/**"
    - '!node_modules/**'

layers:
  Boto3Layer:
    name: Boto3Layer
    description: This layer contains the Boto3 library.
    compatibleRuntimes:
      - python3.10
      - python3.11
    compatibleArchitectures:
      - x86_64
    package:
      artifact: layers/boto3.zip

  OpenAILayer:
    name: OpenAILayer
    description: Layer containing OpenAI library.
    compatibleRuntimes:
      - python3.10
      - python3.11
    compatibleArchitectures:
      - x86_64
    package:
      artifact: layers/open_ai.zip

  PyMongoAWSLayer:
    name: PyMongoAWSLayer
    description: PyMongo library.
    compatibleRuntimes:
      - python3.10
      - python3.11
    compatibleArchitectures:
      - x86_64
    package:
      artifact: layers/pymongoAWS.zip

  RequestsLayer:
    name: RequestsLayer
    description: Requests library.
    compatibleRuntimes:
      - python3.10
      - python3.11
    compatibleArchitectures:
      - x86_64
    package:
      artifact: layers/requests.zip
  
  MsalLayer:
    name: MsalLayer
    description: Msal library.
    compatibleRuntimes:
      - python3.11
      - python3.12
    compatibleArchitectures:
      - x86_64
    package:
      artifact: layers/msal.zip

  PyPDF2Layer:
    name: PyPDF2Layer
    description: PyPDF2 library.
    compatibleRuntimes:
      - python3.11
    compatibleArchitectures:
      - x86_64
    package:
      artifact: layers/pyPDF2.zip

  ParamikoLayer:
    name: ParamikoLayer
    description: Paramiko library.
    compatibleRuntimes:
      - python3.11
    compatibleArchitectures:
      - x86_64
    package:
      artifact: layers/paramiko.zip

  VinInfoLayer:
    name: VinInfoLayer
    description: Vininfo library.
    compatibleRuntimes:
      - python3.11
    compatibleArchitectures:
      - x86_64
    package:
      artifact: layers/vininfo.zip

  OpenpyxlLayer:
    name: OpenpyxlLayer
    description: Openpyxl library.
    compatibleRuntimes:
      - python3.11
    compatibleArchitectures:
      - x86_64
    package:
      artifact: layers/openpyxl.zip

plugins:
  - serverless-deployment-bucket
  - serverless-offline
  - serverless-step-functions
  - serverless-hooks
  - serverless-localstack
  - serverless-plugin-common-excludes
  - serverless-plugin-include-dependencies

custom:
  ecrImageUris:
    local: ""
    snd-hen: "443370719809.dkr.ecr.us-east-1.amazonaws.com/pyautomationaws/selenium:latest"
    prd-hen: "302263076387.dkr.ecr.us-east-1.amazonaws.com/pyautomationaws/selenium:latest"
  deploymentBucketNames:
      local: local-hen-deployment-bucket
      dev: dev-hen-deployment-bucket
      snd-hen: snd-hen-deployment-bucket
      prd-hen: prd-hen-deployment-bucket
  hooks:
    hook:before:package:initialize: bash scripts/prepackage.sh
  stage: ${opt:stage, 'local'}
  service: ${self:service}
  profiles:
    local: snd-hen_qa
    dev: snd-hen_dev
    snd-hen: snd-hen_qa
    prd-hen: hen_prd
  localstack:
    stages:
      - local
    host: http://localhost
  

functions:
  - ${file(resources/functions.${self:provider.stage}.yml):functions}

resources:
  - ${file(resources/roles/bre_handler_role.yml)}
  - ${file(resources/roles/bre_role.yml)}
  - ${file(resources/roles/move_email_role.yml)}
  - ${file(resources/roles/process_email_role.yml)}
  - ${file(resources/roles/email_watcher_role.yml)}
  - ${file(resources/roles/llm_messenger_role.yml)}
  - ${file(resources/roles/pdf_utils.yml)}
  - ${file(resources/roles/process_email_workflow_role.yml)}
  - ${file(resources/roles/llm_extractor_role.yml)}  
  - ${file(resources/roles/report_sftp_to_s3_role.yml)}  
  - ${file(resources/roles/invoice_downloader_role.yml)}  
  - ${file(resources/roles/invoice_to_aria_role.yml)}  
  - ${file(resources/roles/process_invoices_workflow_role.yml)}  
  - ${file(resources/roles/title_sftp_to_s3_to_aria_role.yml)}  
  - ${file(resources/roles/process_titles_workflow_role.yml)}  
  - ${file(resources/roles/reconciliate_role.yml)}  
  - ${file(resources/roles/reevaluate_role.yml)}  
  - ${file(resources/roles/orchestrator_downloader_role.yml)}
  - ${file(resources/roles/orchestrator_download_update_role.yml)}
  - ${file(resources/roles/selenium_downloader_role.yml)}
  - ${file(resources/roles/download_invoices_workflow_role.yml)}
  - ${file(resources/roles/email_to_invoice_flow_role.yml)}
  - ${file(resources/roles/python_handler_role.yml)}
  - ${file(resources/roles/loading_wi_report_role.yml)}
  - ${file(resources/roles/error_wi_report_role.yml)}
  - ${file(resources/roles/report_loaded_data_role.yml)}
  - ${file(resources/roles/lambda_authorizer_role.yml)}
  - ${file(resources/roles/queues_handler_role.yml)}
  - ${file(resources/roles/report_pages_not_used_in_titles_role.yml)}
  - ${file(resources/roles/secrets_handler_role.yml)}
  - ${file(resources/roles/report_to_aria_role.yml)}
  - ${file(resources/roles/reset_passwords_store_workflow_role.yml)}
  - ${file(resources/roles/pricing_guide_role.yml)}
  - ${file(resources/roles/pricing_guide_extractor_role.yml)}
  - ${file(resources/roles/process_pricing_guide_workflow_role.yml)}
  - ${file(resources/roles/bre_used_cars_role.yml)}
  - ${file(resources/roles/llm_extractor_used_cars_role.yml)}
  - ${file(resources/roles/download_new_vehicles_reports_workflow_role.yml)}
  - ${file(resources/roles/load_pre_stock_in_vins_role.yml)}

stepFunctions:
  stateMachines:
    process_pricing_guide:
      id: ProcessPricingGuide
      name: ${self:provider.stage}-process_pricing_guide
      role: !GetAtt ProcessPricingGuideWorkflowRole.Arn
      definition:
        StartAt: DownloadPricingGuide
        States:

          DownloadPricingGuide:
            Type: Task
            Resource:
              Fn::GetAtt: [selenium_downloader, Arn]
            ResultPath: $.downloadResult
            Next: CheckDownloadStatus

          CheckDownloadStatus:
            Type: Choice
            Choices:
              - Variable: "$.downloadResult.statusCode"
                NumericEquals: 200
                Next: ProcessPriceGuide
            Default: EndState

          ProcessPriceGuide:
            Type: Task
            Parameters:
              store.$: $.store
            Resource:
              Fn::GetAtt: [load_pricing_guide, Arn]
            ResultPath: $.result
            Next: ProcessPagesPricingGuide

          ProcessPagesPricingGuide:
            Type: Map
            ItemsPath: $.result.pages_to_process
            Parameters:
              store.$: $.store
              items.$: $$.Map.Item.Value
            Iterator:
              StartAt: "ProcessPagePricingGuide"
              States:
                "ProcessPagePricingGuide":
                  Type: Task
                  Resource:
                    Fn::GetAtt: [load_pricing_guide_extractor, Arn]
                  End: true
            End: true

          EndState:
            Type: Succeed

    process_emails:
      id: ProcessEmails
      name: ${self:provider.stage}-process-emails-v2
      role: !GetAtt ProcessingEmailsWorkflowRole.Arn
      definition:
        StartAt: EmailWatcherH
        States:
          EmailWatcherH:
            Type: Task
            Resource:
              Fn::GetAtt: [email_watcher, Arn]
            ResultPath: $.result
            Next: MapProcessEmail

          MapProcessEmail:
            Type: Map
            ItemsPath: $.result.body.emails_to_be_processed
            Parameters:
              email_id.$: $$.Map.Item.Value
            Iterator:
              StartAt: "ProcessingEmail"
              States:
                "ProcessingEmail":
                  Type: Task
                  Resource:
                    Fn::GetAtt: [process_email, Arn]
                  End: true
            Next: ReportLoadedData

          ReportLoadedData:
            Type: Task
            Resource:
              Fn::GetAtt: [report_loaded_data, Arn]
            Parameters:
              collection: "bol"  
            Next: WaitToProcessAllItems

          WaitToProcessAllItems:
            Type: Wait
            Seconds: 600  # Wait for 30 minutes
            Next: LoadingStuckWiReport

          LoadingStuckWiReport:
            Type: Task
            Resource:
              Fn::GetAtt: [loading_wi_report, Arn]
            Parameters:
              app: "bol"
              stage: "post-inventory"  
            End: true

    process_invoices:
      id: ProcessInvoices
      name: ${self:provider.stage}-process-invoices-v2
      role: !GetAtt ProcessingInvoicesWorkflowRole.Arn
      definition:
        StartAt: InvoiceDownloader
        States:

          InvoiceDownloader:
            Type: Task
            Parameters:
              stage.$: $.stage
            Resource:
              Fn::GetAtt: [invoice_downloader, Arn]
            ResultPath: $.result
            Next: ProcessInvoiceMap

          ProcessInvoiceMap:
            Type: Map
            ItemsPath: $.result.body.vins_to_be_processed
            Parameters:
              stage.$: $.stage
              vin.$: $$.Map.Item.Value
            ResultPath: null  
            Iterator:
              StartAt: "ProcessingInvoice"
              States:
                "ProcessingInvoice":
                  Type: Task
                  Resource:
                    Fn::GetAtt: [invoice_to_aria, Arn]
                  ResultPath: null  
                  End: true

            Next: ReportLoadedData
          
          ReportLoadedData:
            Type: Task
            Parameters:
              stage.$: $.stage
              collection: "invoice"  
            Resource:
              Fn::GetAtt: [report_loaded_data, Arn]
            ResultPath: null              
            Next: WaitToProcessAllItems

          WaitToProcessAllItems:
            Type: Wait
            Seconds: 600  # Wait for 30 minutes
            Next: LoadingStuckWiReport

          LoadingStuckWiReport:
            Type: Task
            Resource:
              Fn::GetAtt: [loading_wi_report, Arn]
            Parameters:
              app: "invoice"  
              stage.$: $.stage
            End: true

    process_title:
      id: ProcessTitle
      name: ${self:provider.stage}-process-title-v2
      role: !GetAtt ProcessingTitlesWorkflowRole.Arn
      definition:
        StartAt: TitleLoader
        States:

          TitleLoader:
            Type: Task
            Resource:
              Fn::GetAtt: [title_sftp_to_s3_to_aria, Arn]
            ResultPath: $.result
            Next: ReportLoadedData

          ReportLoadedData:
            Type: Task
            Resource:
              Fn::GetAtt: [report_loaded_data, Arn]
            Parameters:
              collection: "title"  
            Next: WaitToProcessAllItems

          WaitToProcessAllItems:
            Type: Wait
            Seconds: 600  # Wait for 30 minutes
            Next: LoadingStuckWiReport

          LoadingStuckWiReport:
            Type: Task
            Resource:
              Fn::GetAtt: [loading_wi_report, Arn]
            Parameters:
              app: "title"
              stage: "post-inventory"  
            End: true

    download_invoices:
      id: DownloadInvoices
      name: ${self:provider.stage}-invoice-downloader-orchestrator-v2
      role: !GetAtt DownloadInvoicesWorkflowRole.Arn
      definition:
        StartAt: OrchestratorDownloader
        States:

          OrchestratorDownloader:
            Type: Task
            Parameters:
              stage.$: $.stage
            Resource: 
              Fn::GetAtt: [orchestrator_downloader, Arn]
            ResultPath: $.result
            Next: StoreMap
          
          StoreMap:
            Type: Map
            ItemsPath: $.result.body
            Parameters:
              store.$: $$.Map.Item.Value.store
              batches.$: $$.Map.Item.Value.batches
              stage.$: $.result.stage
            Iterator:
              StartAt: VinBatchMap
              States:
                VinBatchMap:
                  Type: Map
                  ItemsPath: $.batches
                  MaxConcurrency: 1
                  Parameters:
                    stage.$: $.stage
                    store.$: $.store
                    vins.$: $$.Map.Item.Value
                  Iterator:
                    StartAt: SeleniumDownloader
                    States:
                      SeleniumDownloader:
                        Type: Task
                        Resource:
                          Fn::GetAtt: [selenium_downloader, Arn]
                        ResultPath: $.result
                        End: true
                  End: true
            Next: OrchestratorDownloadUpdate

          OrchestratorDownloadUpdate:
            Type: Task
            Resource:
              Fn::GetAtt: [orchestrator_download_update, Arn]
            End: true

    download_and_process_invoice_flow:
      id: DownloadAnProcessInvoicesFlow
      name: ${self:provider.stage}-download-and-process_invoice-flow-v2
      role: !GetAtt EmailToInvoiceFlowRole.Arn
      definition:
        Comment: "Complete flow from reading an email from the invoice to creating it in ARIA"
        StartAt: DownloadInvoiceStep
        States:

          DownloadInvoiceStep:
            Type: Task
            Resource: arn:aws:states:::states:startExecution.sync
            Parameters:
              StateMachineArn: 
                Fn::GetAtt: [DownloadInvoices, Arn]
              Input:
                stage.$: "$.stage"
            ResultPath: null
            Next: WaitToProcessAllItems

          WaitToProcessAllItems:
            Type: Wait
            Seconds: 300  # Wait for 30 minutes
            Next: ProcessInvoiceStep

          ProcessInvoiceStep:
            Type: Task
            Resource: arn:aws:states:::states:startExecution.sync
            Parameters:
              StateMachineArn: 
                Fn::GetAtt: [ProcessInvoices, Arn]
              Input:
                stage.$: "$.stage"
            End: true


    download_new_vehicles_report:
      id: DownloadNewVehiclesReport
      name: ${self:provider.stage}-download_new_vehicles_report
      role: !GetAtt DownloadNewVehiclesReportWorkflowRole.Arn
      definition:
        StartAt: OrchestratorDownloader
        States:

          OrchestratorDownloader:
            Type: Task
            Parameters:
              stage.$: $.stage
              action.$: $.action
            Resource: 
              Fn::GetAtt: [orchestrator_downloader, Arn]
            ResultPath: $.result
            Next: StoreMap
          
          StoreMap:
            Type: Map
            ItemsPath: $.result.body
            Parameters:
              store.$: $$.Map.Item.Value.store
              action.$: $.result.action
              stage.$: $.result.stage
            Iterator:
              StartAt: SeleniumDownloader
              States:
                SeleniumDownloader:
                  Type: Task
                  Resource:
                    Fn::GetAtt: [selenium_downloader, Arn]
                  ResultPath: $.result
                  End: true
            End: true


    # reset_password_for_store:
    #   name: ${self:provider.stage}-reset_password_for_store
    #   role: !GetAtt ResetPasswordForStoreRole.Arn
    #   definition:
    #     StartAt: GetSupportedStores
    #     States:

    #       GetSupportedStores:
    #         Type: Task
    #         Resource: arn:aws:states:::aws-sdk:secretsmanager:getSecretValue
    #         Parameters:
    #           SecretId: ${self:provider.stage}-supported_stores
    #         ResultSelector:
    #           # This converts the stringified array into a real array
    #           stores.$: "States.StringToJson($.SecretString)"
    #         ResultPath: $.result
    #         Next: IterateStore

    #       IterateStore:
    #         Type: Map
    #         ItemsPath: $.result.stores
    #         Parameters:
    #             store.$: $$.Map.Item.Value
    #             action: "reset_password"
    #         Iterator:
    #           StartAt: "ResetLoginPassword"
    #           States:
    #             "ResetLoginPassword":
    #               Type: Task
    #               Resource:
    #                 Fn::GetAtt: [selenium_downloader, Arn]
    #               End: true
    #         End: true
