
def get_stepfunctions_config(env):
    return {
        "process_pricing_guide": {
            'name': f'{env}-process_pricing_guide',
            'definition_json_path': 'stepfunctions/process_pricing_guide.json',
            'role_name': 'process_price_guide_workflow'
        },

        "download_and_process_invoice_flow":  {
            'name': f'{env}-download-and-process_invoice-flow',
            'definition_json_path': 'stepfunctions/download_and_process_invoice_flow.json',
            'role_name': 'process_email_workflow'
        },

        "invoice-downloader-orchestrator":  {
            'name': f'{env}-invoice-downloader-orchestrator',
            'definition_json_path': 'stepfunctions/invoice_downloader_orchestrator.json',
            'role_name': 'download_invoices_workflow'
        },

        "process_titles":  {
            'name': f'{env}-process-title',
            'definition_json_path': 'stepfunctions/process_title.json',
            'role_name': 'process_titles_workflow'
        },


        "process_invoices": {
            'name': f'{env}-process-invoices',
            'definition_json_path': 'stepfunctions/process_invoices.json',
            'role_name': 'process_invoices_workflow'
        },

        "process_emails": {
            'name': f'{env}-process-emails',
            'definition_json_path': 'stepfunctions/process_emails.json',
            'role_name': 'process_email_workflow'
        },


        "download_new_vehicles_reports": {
            'name': f'{env}-download_new_vehicles_reports',
            'definition_json_path': 'stepfunctions/load_pre_inventory.json',
            'role_name': 'download_new_vehicles_reports_workflow'
        }
    }