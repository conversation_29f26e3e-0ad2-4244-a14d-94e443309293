# This module contains a class for interacting with the Emails collection in the MongoDB database.

import os
import datetime

from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudReynolsReport:
    def __init__(self):
        mongo_uri = get_secret(os.environ['ENV'] + '-mongodb_uri', return_json=False)
        self.mongo = Mongo(mongo_uri)
        self.mongo.select_db_and_collection(
            db_name=os.environ['MONGO_DATABASE'],
            collection_name='vin'
        )
        self.store_path = "flows.post-inventory.report-data.store"
        self.downloaded_path = "flows.post-inventory.docs.invoice.downloaded_at"
        self.supported_stores = get_secret(os.environ['ENV'] + '-supported_stores', return_json=True)

    def insert_row(self, reynols_row, report_name):
        """
        This function inserts a reynols report row into the database.
        STORE	RECEIVED	STOCK #	MAKE	DESC	VIN	INV AMT	SLS COST	STK IN NOTES	SVC RO DATE	STAT-CODE	JRNL-PURCH-DATE-DR
        """
        reynols_row_to_db = {
            "store": reynols_row['STORE'],
            "received": reynols_row['RECEIVED'],
            "stock": reynols_row['STOCK #'],
            "make": reynols_row['MAKE'],
            "desc": reynols_row['DESC'],
            "vin": reynols_row['VIN'],
            "inv_amt": reynols_row['INV AMT'],
            "sls_cost": reynols_row['SLS COST'],
            "stock_in_notes": reynols_row['STK IN NOTES'],
            "svc_ro_date": reynols_row['SVC RO DATE'],
            "stat_code": reynols_row['STAT-CODE'],
            "jrnl_purch_date_dr": reynols_row['JRNL-PURCH-DATE-DR'],
            "read_at": datetime.datetime.now(),
            "first_read_from_file": report_name
        }

        self.mongo.insert_one(reynols_row_to_db)
        return reynols_row_to_db

    def update_row(self, reynols_row, report_name, vin):
        """
        This function inserts a reynols report row into the database.
        STORE	RECEIVED	STOCK #	MAKE	DESC	VIN	INV AMT	SLS COST	STK IN NOTES	SVC RO DATE	STAT-CODE	JRNL-PURCH-DATE-DR
        """
        reynols_row_to_db = {
            "store": reynols_row['STORE'],
            "received": reynols_row['RECEIVED'],
            "stock": reynols_row['STOCK #'],
            "make": reynols_row['MAKE'],
            "desc": reynols_row['DESC'],
            "inv_amt": reynols_row['INV AMT'],
            "sls_cost": reynols_row['SLS COST'],
            "stock_in_notes": reynols_row['STK IN NOTES'],
            "svc_ro_date": reynols_row['SVC RO DATE'],
            "stat_code": reynols_row['STAT-CODE'],
            "jrnl_purch_date_dr": reynols_row['JRNL-PURCH-DATE-DR'],
            "last_updated_at": datetime.datetime.now(),
            "last_read_from_file": report_name
        }

        self.mongo.update_one({"vin": vin}, {"$set": reynols_row_to_db})
        return reynols_row_to_db
    
    def update_many(self, filter, data):
        """
        This function updates multiple rows in the database.
        """
        self.mongo.update_many(filter, data)

    def find_report_row_by_vin(self, vin):
        """
        This function finds an email by its email ID.
        """
        query = {"vin": vin}
        return self.mongo.find_one(query)
    
    def get_vins_to_download(self):
        """
        This function retrieves all VINs that have not been downloaded yet.
        - VINs in status 0 or 2; they can be retried every hour until they are downloaded
        - VINs in status 1; they can be retried every 4 hours and just before 3 days have passed since they were read
        """
        query = {
            self.downloaded_path: {"$in": [None, ""]},
            '$or': [
                {'status': 0},
                {'status': 2},
                {'status': 1, 'read_at': {"$lte": datetime.datetime.now() - datetime.timedelta(days=2, hours=23)}},
                {'status': 1, 'updated_at': {"$lte": datetime.datetime.now() - datetime.timedelta(hours=4)}}
            ]
        }

        stores = self.mongo.select_distinct(query, self.store_path)
        # Filter only supported stores
        stores = [store for store in stores if store in self.supported_stores]

        vins_per_store = []
        for store in stores:
            query[self.store_path] = store
            vins_per_store.append({
                "store": store,
                "vins": self.mongo.select_distinct(query, "vin")
            })

        return vins_per_store
    
    def get_vins_to_report(self):
        """
        This function retrieves all VINs that have not been downloaded and need to be reported.
        """
        query = {
            self.downloaded_path: {"$in": [None, ""]},
            'status': {'$in': [0, 1, 2]},
            "read_at": {"$lt": datetime.datetime.now() - datetime.timedelta(days=3)},
            self.store_path: {"$in": self.supported_stores}
        }

        return list(self.mongo.find(query))
    

    def get_vins_info_of_selected(self, vins):
        """
        This function retrieves all VINs that have not been downloaded and need to be reported.
        """
        query = {
            'vin': {"$in": vins}
        }

        return list(self.mongo.find(query))


    def __del__(self):
        self.mongo.close_connection()
