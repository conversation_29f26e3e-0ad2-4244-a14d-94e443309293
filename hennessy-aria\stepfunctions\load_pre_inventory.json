{"StartAt": "OrchestratorDownloader", "States": {"OrchestratorDownloader": {"Type": "Task", "Parameters": {"stage.$": "$.stage", "action.$": "$.action"}, "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-orchestrator_downloader", "ResultPath": "$.result", "Next": "StoreMap"}, "StoreMap": {"Type": "Map", "ItemsPath": "$.result.body", "Parameters": {"store.$": "$$.Map.Item.Value.store", "action.$": "$.result.action", "stage.$": "$.result.stage"}, "Iterator": {"StartAt": "SeleniumDownloader", "States": {"SeleniumDownloader": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-selenium_downloader", "ResultPath": "$.result", "Next": "PreStockInLoader"}, "PreStockInLoader": {"Type": "Task", "Parameters": {"store.$": "$.store"}, "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-pre_stock_in_vins", "ResultPath": "$.result", "End": true}}}, "End": true}}}