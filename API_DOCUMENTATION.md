# 🔌 API Documentation - Hennessy Invoice Processing System

## 📋 Overview

This document provides comprehensive API documentation for all available endpoints and functions in the Hennessy system.

## 🏗️ Architecture

The system consists of multiple AWS Lambda functions orchestrated through Step Functions and API Gateway:

```
API Gateway → Lambda Functions → MongoDB/S3 → External Systems
```

## 🔑 Authentication

All API calls require proper AWS IAM permissions or API Gateway authentication.

### **Headers Required:**
```http
Content-Type: application/json
Authorization: Bearer <your-token>  # If using API Gateway
```

## 📡 Core APIs

### **1. Invoice Download API**

Downloads invoices from vendor portals for specified VINs.

#### **Endpoint:** `POST /invoice-download`

#### **Request:**
```json
{
  "store": "FOR",                    // Store code (FOR, LOA, LOG, JLRN, JLRB, POR, PNW, CAD)
  "vins": [                         // Array of Vehicle Identification Numbers
    "1FTFW1ET5DFC12345",
    "1FTFW1ET5DFC67890"
  ],
  "action": "invoice_download",      // Action type
  "date_range": {                   // Optional: Date range filter
    "start": "2024-01-01",
    "end": "2024-01-31"
  }
}
```

#### **Response:**
```json
{
  "statusCode": 200,
  "message": "Invoices downloaded successfully",
  "results": [
    {
      "vin": "1FTFW1ET5DFC12345",
      "status": "success",
      "files_downloaded": [
        "s3://bucket/invoices/FOR_1FTFW1ET5DFC12345_invoice.pdf",
        "s3://bucket/invoices/FOR_1FTFW1ET5DFC12345_bol.pdf"
      ]
    },
    {
      "vin": "1FTFW1ET5DFC67890", 
      "status": "not_found",
      "message": "No invoices found for this VIN"
    }
  ],
  "execution_time": "45.2s",
  "total_files": 2
}
```

#### **Error Response:**
```json
{
  "statusCode": 500,
  "error": "LoginError",
  "message": "Failed to login to vendor portal",
  "details": "Invalid credentials for store FOR"
}
```

---

### **2. Business Rules Engine (BRE) API**

Processes invoice data through business rules and determines next actions.

#### **Endpoint:** `POST /bre-handler`

#### **Request:**
```json
{
  "body": {
    "vin": "1FTFW1ET5DFC12345",
    "store": "FOR",
    "invoice_data": {
      "invoice_number": "INV-12345",
      "amount": 25000.00,
      "date": "2024-01-15",
      "vendor": "Ford Motor Company"
    },
    "document_s3_path": "s3://bucket/invoices/FOR_1FTFW1ET5DFC12345.pdf"
  }
}
```

#### **Response:**
```json
{
  "statusCode": 200,
  "bre_result": "approved",
  "next_action": "send_to_aria",
  "execution_id": "550e8400-e29b-41d4-a716-446655440000",
  "confidence_score": 0.95,
  "validation_results": {
    "amount_valid": true,
    "date_valid": true,
    "vin_match": true,
    "vendor_verified": true
  },
  "recommended_actions": [
    "process_payment",
    "update_inventory"
  ]
}
```

---

### **3. Document Processing API**

Extracts data from PDF documents using AI/OCR.

#### **Endpoint:** `POST /pdf-utils`

#### **Request:**
```json
{
  "action": "process_document",
  "s3_path": "s3://bucket/invoices/invoice.pdf",
  "extract_fields": [
    "invoice_number",
    "amount", 
    "date",
    "vin",
    "vendor_name",
    "line_items"
  ],
  "ocr_engine": "textract",          // Options: textract, tesseract
  "ai_enhancement": true             // Use AI for better extraction
}
```

#### **Response:**
```json
{
  "statusCode": 200,
  "extracted_data": {
    "invoice_number": "INV-12345",
    "amount": "$25,000.00",
    "date": "2024-01-15",
    "vin": "1FTFW1ET5DFC12345",
    "vendor_name": "Ford Motor Company",
    "line_items": [
      {
        "description": "Vehicle Purchase",
        "amount": "$24,000.00"
      },
      {
        "description": "Documentation Fee",
        "amount": "$1,000.00"
      }
    ]
  },
  "confidence_scores": {
    "invoice_number": 0.99,
    "amount": 0.95,
    "date": 0.98,
    "vin": 0.97
  },
  "processing_time": "3.2s"
}
```

---

### **4. LLM Messenger API**

Interfaces with Large Language Models for data extraction and processing.

#### **Endpoint:** `POST /llm-messenger`

#### **Request:**
```json
{
  "provider": "openai",              // Options: openai, bedrock
  "host": "public",                  // Options: public, azure
  "llm_model": "gpt-4",             // Model name
  "prompt": "Extract invoice data from the following document",
  "message": "Please extract all relevant invoice information",
  "files": [                        // Optional: S3 file paths
    "s3://bucket/invoices/invoice.pdf"
  ],
  "response_format": "json"         // Optional: json, text
}
```

#### **Response:**
```json
{
  "statusCode": 200,
  "body": {
    "message": {
      "invoice_number": "INV-12345",
      "amount": 25000.00,
      "date": "2024-01-15",
      "extracted_text": "Full extracted text...",
      "confidence": 0.94
    }
  }
}
```

---

### **5. Email Processing API**

Processes incoming emails and attachments.

#### **Endpoint:** `POST /email-watcher`

#### **Request:**
```json
{
  "email_id": "msg_12345",
  "sender": "<EMAIL>",
  "subject": "Invoice for VIN 1FTFW1ET5DFC12345",
  "attachments": [
    {
      "filename": "invoice.pdf",
      "s3_path": "s3://bucket/emails/attachments/invoice.pdf"
    }
  ],
  "body_text": "Please find attached invoice..."
}
```

#### **Response:**
```json
{
  "statusCode": 200,
  "processed": true,
  "actions_taken": [
    "extracted_vin",
    "downloaded_attachment", 
    "triggered_bre"
  ],
  "extracted_vins": ["1FTFW1ET5DFC12345"],
  "next_steps": ["process_invoice"]
}
```

---

### **6. Reconciliation API**

Reconciles invoices with BOL (Bill of Lading) and title documents.

#### **Endpoint:** `POST /reconciliate`

#### **Request:**
```json
{
  "date_range": {
    "start": "2024-01-01",
    "end": "2024-01-31"
  },
  "store_filter": ["FOR", "LOA"],    // Optional: Filter by stores
  "status_filter": ["pending"]       // Optional: Filter by status
}
```

#### **Response:**
```json
{
  "statusCode": 200,
  "reconciliation_results": {
    "total_invoices": 150,
    "matched": 120,
    "unmatched": 30,
    "discrepancies": [
      {
        "vin": "1FTFW1ET5DFC12345",
        "issue": "missing_bol",
        "invoice_amount": 25000.00,
        "expected_amount": 24500.00
      }
    ]
  },
  "processing_time": "12.5s"
}
```

---

### **7. Execution Report API**

Generates execution reports for processed folios.

#### **Endpoint:** `GET /execution-report`

#### **Query Parameters:**
```
?app_id=12345&hours_back=24&format=json
```

#### **Response:**
```json
{
  "folios": [
    {
      "aria_wi_id": "WI-12345",
      "vin": "1FTFW1ET5DFC12345",
      "status": "completed",
      "last_updated": "2024-01-15T10:30:00Z"
    }
  ],
  "app_id": "12345",
  "report_period": "2024-01-14T10:30:00Z to 2024-01-15T10:30:00Z",
  "total_folios": 25
}
```

## 🔄 Workflow APIs

### **Step Functions Integration**

The system uses AWS Step Functions for complex workflows:

#### **Start Workflow:**
```json
{
  "input": {
    "store": "FOR",
    "vins": ["1FTFW1ET5DFC12345"],
    "workflow_type": "full_processing"
  }
}
```

#### **Workflow Steps:**
1. `invoice_downloader` - Download invoices
2. `pdf_utils` - Extract data
3. `bre_handler` - Apply business rules
4. `invoice_to_aria` - Send to ERP
5. `reconciliate` - Final reconciliation

## 🚨 Error Handling

### **Common Error Codes:**

| Code | Description | Solution |
|------|-------------|----------|
| 400 | Bad Request | Check request format |
| 401 | Unauthorized | Verify credentials |
| 404 | Not Found | Check VIN/resource exists |
| 429 | Rate Limited | Implement retry logic |
| 500 | Internal Error | Check logs, retry |
| 503 | Service Unavailable | Vendor portal down |

### **Error Response Format:**
```json
{
  "statusCode": 500,
  "error": "ProcessingError",
  "message": "Failed to process document",
  "details": {
    "function": "pdf_utils",
    "step": "ocr_extraction",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "retry_after": 300
}
```

## 📊 Rate Limits

| API | Limit | Window |
|-----|-------|--------|
| Invoice Download | 10 req/min | Per store |
| Document Processing | 50 req/min | Global |
| LLM Messenger | 20 req/min | Per model |
| BRE Handler | 100 req/min | Global |

## 🔧 Testing

### **Test Endpoints:**

All APIs support a `test_mode` parameter for safe testing:

```json
{
  "test_mode": true,
  "store": "FOR",
  "vins": ["TEST123456789"]
}
```

### **Mock Responses:**

Use `mock=true` parameter to get sample responses without actual processing.

## 📝 SDK Examples

### **Python SDK:**
```python
import requests

def download_invoices(store, vins):
    response = requests.post(
        'https://api.hennessy.com/invoice-download',
        json={
            'store': store,
            'vins': vins,
            'action': 'invoice_download'
        },
        headers={'Authorization': 'Bearer your-token'}
    )
    return response.json()

# Usage
result = download_invoices('FOR', ['1FTFW1ET5DFC12345'])
```

### **Node.js SDK:**
```javascript
const axios = require('axios');

async function processDocument(s3Path) {
  const response = await axios.post(
    'https://api.hennessy.com/pdf-utils',
    {
      action: 'process_document',
      s3_path: s3Path,
      extract_fields: ['invoice_number', 'amount', 'date']
    },
    {
      headers: { 'Authorization': 'Bearer your-token' }
    }
  );
  return response.data;
}
```

## 🔍 Monitoring

### **Health Check:**
```http
GET /health
```

### **Metrics:**
- Request count
- Response time
- Error rate
- Success rate

### **Logs:**
All APIs log to AWS CloudWatch with structured JSON format.
