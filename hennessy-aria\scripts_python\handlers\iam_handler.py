import boto3
import yaml
import json

class IAMHandler():

    def __init__(self, profile):
        self.boto3 = boto3.Session(profile_name=profile, region_name='us-east-1')

    def create_or_update_role(self, role_name, assume_role_policy, policies):
        iam_client = self.boto3.client('iam')

        try:
            # Check if the role already exists
            role = iam_client.get_role(RoleName=role_name)

            # Update the assume role policy if needed
            iam_client.update_assume_role_policy(
                RoleName=role_name,
                PolicyDocument=json.dumps(assume_role_policy)
            )

            # Detach existing policies
            existing_policies = iam_client.list_attached_role_policies(RoleName=role_name)
            for policy in existing_policies['AttachedPolicies']:
                iam_client.detach_role_policy(
                    RoleName=role_name,
                    PolicyArn=policy['PolicyArn']
                )

        except iam_client.exceptions.NoSuchEntityException:
            # Role does not exist, create it
            role = iam_client.create_role(
                RoleName=role_name,
                AssumeRolePolicyDocument=json.dumps(assume_role_policy)
            )

        # Attach policies to the role
        for policy in policies:
            policy_name = policy['PolicyName']
            policy_document = policy['PolicyDocument']

            # Create the policy
            iam_client.put_role_policy(
                RoleName=role_name,
                PolicyName=policy_name,
                PolicyDocument=json.dumps(policy_document)
            )

        return role['Role']['Arn']


    def create_update_policies(self, stage_string, yaml_path):
        # Load and replace placeholders in the YAML file
        with open(yaml_path, 'r') as f:
            content = f.read().replace('${self:provider.stage}', stage_string)

        config = yaml.safe_load(content)

        for role_key, role_info in config['Resources'].items():
            role_properties = role_info['Properties']
            role_name = role_properties['RoleName']
            assume_role_policy = role_properties['AssumeRolePolicyDocument']
            policies = role_properties.get('Policies', [])

            return self.create_or_update_role(role_name, assume_role_policy, policies)



    