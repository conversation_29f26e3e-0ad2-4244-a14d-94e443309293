from Utilities.Common_Utilities.logger_utility import Logger
import json
import traceback

class Json():

    def __init__(self, log_path):
        self.l = Logger(log_path)

    #Parse string into json
    #   INPUT
    #       -text
    #   OUTPUT
    #       -json_parsed
    def load_json(self, text):
        try:
            json_parsed = json.loads(text)
            return json_parsed
        except Exception as e:
            raise Exception ('Exception occurred on load_json method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))