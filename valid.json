{"_id": {"$oid": "687e2a5b0d1d5c7eaa91ffc9"}, "validation_type_key": "tag_titles", "properties": {"bos": {"fields": {"deal_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9]+$", "error_msg": "Deal number is required"}, {"check": "alphanumeric", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9]+$", "error_msg": "Deal number should be alphanumeric"}]}, "stock_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9-]{1,20}$", "error_msg": "Stock number is required and should be alphanumeric with hyphens (1-20 characters)"}]}, "vin": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-HJ-NPR-Z0-9]{17}$", "error_msg": "VIN is required"}, {"check": "vin_format", "isValidationType": "REGEX", "regex": "^[A-HJ-NPR-Z0-9]{17}$", "error_msg": "VIN should be exactly 17 characters (excluding I, O, Q)"}]}, "year": {"required": true, "type": "integer", "validation_rules": [{"check": "not_empty", "isValidationType": "NUMERIC", "min_value": 1900, "error_msg": "Year is required"}, {"check": "year_range", "isValidationType": "NUMERIC", "min_value": 1900, "max_value": 2030, "error_msg": "Year should be between 1900 and 2030"}]}, "make": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"}]}, "model": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,30}$", "error_msg": "Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)"}]}, "odometer_reading": {"required": false, "type": "integer", "validation_rules": [{"check": "positive_number", "isValidationType": "NUMERIC", "min_value": 0, "max_value": 999999, "error_msg": "Odometer reading should be a positive number between 0 and 999,999"}]}, "buyer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer name is required"}, {"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Buyer name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "co_buyer_name": {"required": false, "type": "string", "validation_rules": [{"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Co-buyer name should contain only letters, spaces, hyphens and apostrophes"}]}, "buyer_address": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer address is required"}, {"check": "address_format", "isValidationType": "REGEX", "regex": "^[0-9]+\\s+[A-Za-z0-9\\s,.-]+,\\s*[A-Za-z\\s]+,\\s*[A-Z]{2}\\s+[0-9]{5}(-[0-9]{4})?$", "error_msg": "Address should be in format: Street, City, State ZIP"}]}, "sale_price": {"required": true, "type": "decimal", "validation_rules": [{"check": "not_empty", "isValidationType": "NUMERIC", "min_value": 0.01, "error_msg": "Sale price is required"}, {"check": "positive_amount", "isValidationType": "NUMERIC", "min_value": 0.01, "max_value": 999999.99, "error_msg": "Sale price should be a positive amount between $0.01 and $999,999.99"}]}, "trade_in_value": {"required": false, "type": "decimal", "validation_rules": [{"check": "positive_amount", "isValidationType": "NUMERIC", "min_value": 0, "max_value": 999999.99, "error_msg": "Trade-in value should be a positive amount up to $999,999.99"}]}, "tavt_tax_amount": {"required": true, "type": "decimal", "validation_rules": [{"check": "not_empty", "isValidationType": "NUMERIC", "min_value": 0, "error_msg": "TAVT tax amount is required"}, {"check": "positive_amount", "isValidationType": "NUMERIC", "min_value": 0, "max_value": 99999.99, "error_msg": "TAVT tax amount should be between $0 and $99,999.99"}]}, "total_amount_due": {"required": false, "type": "decimal", "validation_rules": [{"check": "positive_amount", "isValidationType": "NUMERIC", "min_value": 0, "max_value": 999999.99, "error_msg": "Total amount due should be a positive amount up to $999,999.99"}]}, "lien_holder_name": {"required": false, "type": "string", "validation_rules": [{"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$", "error_msg": "Lien holder name should be 2-100 characters with valid business name characters"}]}}}, "dl": {"fields": {"full_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Full name is required"}, {"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Full name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "date_of_birth": {"required": true, "type": "date", "validation_rules": [{"check": "not_empty", "isValidationType": "DATE", "error_msg": "Date of birth is required"}, {"check": "valid_date", "isValidationType": "DATE", "date_format": "MM/DD/YYYY", "error_msg": "Date of birth should be in MM/DD/YYYY format"}, {"check": "age_verification", "isValidationType": "DATE", "min_age": 16, "max_age": 120, "error_msg": "Date of birth indicates age should be between 16 and 120 years"}]}, "address__street": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Street address is required"}, {"check": "address_format", "isValidationType": "REGEX", "regex": "^[0-9]+\\s+[A-Za-z0-9\\s,.-]{1,100}$", "error_msg": "Street address should start with a number followed by street name (1-100 characters)"}]}, "city": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "City is required"}, {"check": "city_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "City should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "state": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "State is required"}, {"check": "state_format", "isValidationType": "REGEX", "regex": "^[A-Z]{2}$", "error_msg": "State should be a 2-letter abbreviation (e.g., GA, FL)"}]}, "zip": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "ZIP code is required"}, {"check": "zip_format", "isValidationType": "REGEX", "regex": "^[0-9]{5}(-[0-9]{4})?$", "error_msg": "ZIP code should be 5 digits or 5+4 format (e.g., 12345 or 12345-6789)"}]}, "driver_s_license_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Driver's license number is required"}, {"check": "license_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9]{6,20}$", "error_msg": "Driver's license number should be 6-20 alphanumeric characters"}]}}}, "mv1": {"fields": {"buyer_full_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer full name is required"}, {"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Buyer full name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "co_buyer_name": {"required": false, "type": "string", "validation_rules": [{"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Co-buyer name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "buyer_address": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer address is required"}, {"check": "address_format", "isValidationType": "REGEX", "regex": "^[0-9]+\\s+[A-Za-z0-9\\s,.-]{1,100}$", "error_msg": "Buyer address should start with a number followed by street name (1-100 characters)"}]}, "city": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "City is required"}, {"check": "city_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "City should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "state": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "State is required"}, {"check": "state_format", "isValidationType": "REGEX", "regex": "^[A-Z]{2}$", "error_msg": "State should be a 2-letter abbreviation (e.g., GA, FL)"}]}, "zip": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "ZIP code is required"}, {"check": "zip_format", "isValidationType": "REGEX", "regex": "^[0-9]{5}(-[0-9]{4})?$", "error_msg": "ZIP code should be 5 digits or 5+4 format (e.g., 12345 or 12345-6789)"}]}, "county_of_residence": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "County of residence is required"}, {"check": "county_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "County should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "customer_id": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Customer ID (Driver's License Number) is required"}, {"check": "license_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9]{6,20}$", "error_msg": "Customer ID should be 6-20 alphanumeric characters"}]}, "vin": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-HJ-NPR-Z0-9]{17}$", "error_msg": "VIN is required"}, {"check": "vin_format", "isValidationType": "REGEX", "regex": "^[A-HJ-NPR-Z0-9]{17}$", "error_msg": "VIN should be exactly 17 characters (excluding I, O, Q)"}]}, "year": {"required": true, "type": "integer", "validation_rules": [{"check": "not_empty", "isValidationType": "NUMERIC", "min_value": 1900, "error_msg": "Year is required"}, {"check": "year_range", "isValidationType": "NUMERIC", "min_value": 1900, "max_value": 2030, "error_msg": "Year should be between 1900 and 2030"}]}, "make": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"}]}, "model": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,30}$", "error_msg": "Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)"}]}, "body_style": {"required": false, "type": "string", "validation_rules": [{"check": "body_style_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,20}$", "error_msg": "Body style should contain only letters, numbers, spaces, and hyphens (1-20 characters)"}]}, "odometer_reading": {"required": true, "type": "integer", "validation_rules": [{"check": "not_empty", "isValidationType": "NUMERIC", "min_value": 0, "error_msg": "Odometer reading is required"}, {"check": "positive_number", "isValidationType": "NUMERIC", "min_value": 0, "max_value": 999999, "error_msg": "Odometer reading should be between 0 and 999,999"}]}, "lien_holder_name": {"required": false, "type": "string", "validation_rules": [{"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$", "error_msg": "Lien holder name should be 2-100 characters with valid business name characters"}]}, "lien_holder_address": {"required": false, "type": "string", "validation_rules": [{"check": "address_format", "isValidationType": "REGEX", "regex": "^[0-9]+\\s+[A-Za-z0-9\\s,.-]+,\\s*[A-Za-z\\s]+,\\s*[A-Z]{2}\\s+[0-9]{5}(-[0-9]{4})?$", "error_msg": "Lien holder address should be in format: Street, City, State ZIP"}]}, "dealer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Dealer name is required"}, {"check": "dealer_name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$", "error_msg": "Dealer name should be 2-100 characters with valid business name characters"}]}, "dealer_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Dealer number is required"}, {"check": "dealer_number_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9]{3,20}$", "error_msg": "Dealer number should be 3-20 alphanumeric characters"}]}, "sale_price": {"required": true, "type": "decimal", "validation_rules": [{"check": "not_empty", "isValidationType": "NUMERIC", "min_value": 0.01, "error_msg": "Sale price is required"}, {"check": "positive_amount", "isValidationType": "NUMERIC", "min_value": 0.01, "max_value": 999999.99, "error_msg": "Sale price should be a positive amount between $0.01 and $999,999.99"}]}}}, "mv7d": {"fields": {"vin": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-HJ-NPR-Z0-9]{17}$", "error_msg": "VIN is required"}, {"check": "vin_format", "isValidationType": "REGEX", "regex": "^[A-HJ-NPR-Z0-9]{17}$", "error_msg": "VIN should be exactly 17 characters (excluding I, O, Q)"}]}, "year": {"required": true, "type": "integer", "validation_rules": [{"check": "not_empty", "isValidationType": "NUMERIC", "min_value": 1900, "error_msg": "Year is required"}, {"check": "year_range", "isValidationType": "NUMERIC", "min_value": 1900, "max_value": 2030, "error_msg": "Year should be between 1900 and 2030"}]}, "make": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"}]}, "model": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,30}$", "error_msg": "Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)"}]}, "odometer_reading": {"required": true, "type": "integer", "validation_rules": [{"check": "not_empty", "isValidationType": "NUMERIC", "min_value": 0, "error_msg": "Odometer reading is required"}, {"check": "positive_number", "isValidationType": "NUMERIC", "min_value": 0, "max_value": 999999, "error_msg": "Odometer reading should be between 0 and 999,999"}]}, "odometer_type": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Odometer type is required"}, {"check": "odometer_type_valid", "isValidationType": "REGEX", "regex": "^(Actual|Exceeds|Not Actual)$", "error_msg": "Odometer type should be 'Actual', 'Exceeds', or 'Not Actual'"}]}, "buyer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer name is required"}, {"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Buyer name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "buyer_address": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer address is required"}, {"check": "address_format", "isValidationType": "REGEX", "regex": "^[0-9]+\\s+[A-Za-z0-9\\s,.-]{1,100}$", "error_msg": "Buyer address should start with a number followed by street name (1-100 characters)"}]}, "date_of_reassignment": {"required": true, "type": "date", "validation_rules": [{"check": "not_empty", "isValidationType": "DATE", "error_msg": "Date of reassignment is required"}, {"check": "valid_date", "isValidationType": "DATE", "date_format": "MM/DD/YYYY", "error_msg": "Date of reassignment should be in MM/DD/YYYY format"}, {"check": "date_range", "isValidationType": "DATE", "min_date": "01/01/2000", "max_date": "current_date", "error_msg": "Date of reassignment should be between 2000 and current date"}]}}}, "title": {"fields": {"vin": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-HJ-NPR-Z0-9]{17}$", "error_msg": "VIN is required"}, {"check": "vin_format", "isValidationType": "REGEX", "regex": "^[A-HJ-NPR-Z0-9]{17}$", "error_msg": "VIN should be exactly 17 characters (excluding I, O, Q)"}]}, "year": {"required": true, "type": "integer", "validation_rules": [{"check": "not_empty", "isValidationType": "NUMERIC", "min_value": 1900, "error_msg": "Year is required"}, {"check": "year_range", "isValidationType": "NUMERIC", "min_value": 1900, "max_value": 2030, "error_msg": "Year should be between 1900 and 2030"}]}, "make": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"}]}, "model": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,30}$", "error_msg": "Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)"}]}, "body_style": {"required": false, "type": "string", "validation_rules": [{"check": "body_style_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,20}$", "error_msg": "Body style should contain only letters, numbers, spaces, and hyphens (1-20 characters)"}]}, "odometer_reading": {"required": true, "type": "integer", "validation_rules": [{"check": "not_empty", "isValidationType": "NUMERIC", "min_value": 0, "error_msg": "Odometer reading is required"}, {"check": "positive_number", "isValidationType": "NUMERIC", "min_value": 0, "max_value": 999999, "error_msg": "Odometer reading should be between 0 and 999,999"}]}, "selling_dealer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Selling dealer name is required"}, {"check": "dealer_name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$", "error_msg": "Selling dealer name should be 2-100 characters with valid business name characters"}]}, "lien_holder_name": {"required": false, "type": "string", "validation_rules": [{"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$", "error_msg": "Lien holder name should be 2-100 characters with valid business name characters"}]}, "lien_satisfied": {"required": false, "type": "boolean", "validation_rules": [{"check": "boolean_format", "isValidationType": "Boolean", "error_msg": "Lien satisfied should be true or false"}]}, "date_of_transfer": {"required": true, "type": "date", "validation_rules": [{"check": "not_empty", "isValidationType": "DATE", "error_msg": "Date of transfer is required"}, {"check": "valid_date", "isValidationType": "DATE", "date_format": "MM/DD/YYYY", "error_msg": "Date of transfer should be in MM/DD/YYYY format"}, {"check": "date_range", "isValidationType": "DATE", "min_date": "01/01/2000", "max_date": "current_date", "error_msg": "Date of transfer should be between 2000 and current date"}]}, "buyer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer name is required"}, {"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Buyer name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "title_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Title number is required"}, {"check": "title_number_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9]{6,20}$", "error_msg": "Title number should be 6-20 alphanumeric characters"}]}}}}}