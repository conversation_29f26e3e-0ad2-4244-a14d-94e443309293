# This module contains utility functions and a class for interacting with the Outlook API.

import os
import msal
import base64
import requests
from datetime import datetime
import smtplib
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.text import MIMEText

from boto3_utils import get_secret

IDENTITY_ENDPOINT = 'https://login.microsoftonline.com/'
GRAPH_ENDPOINT = 'https://graph.microsoft.com/v1.0/users/'

def print_error_details(message, response):
    """
    This function prints the details of the error response.
    """
    print(message)
    print('Status code:', response.status_code)
    print('Response content:', response.json())


# class Outlook:
#     """
#     This class provides methods to interact with the Outlook API.
#     """
#     def __init__(self, client):
#         """
#         This method initializes the Outlook class with the client credentials.
#         """
#         credentials = get_secret(os.environ['ENV'] + '-email_credentials')
#         self.config = credentials[client]
#         self.tenan_id = self.config['TENANT_ID']
#         self.user_id = self.config['USERID']
#         self.client_id = self.config['CLIENT_ID']
#         self.client_secret = self.config['CLIENT_SECRET']
        

#     def get_access_token(self):
#         """
#         This function retrieves an access token from Azure AD using the client credentials flow.
#         """
#         authority_url = f"{IDENTITY_ENDPOINT}{self.tenan_id}"
#         scopes = ['https://graph.microsoft.com/.default']

#         # Initialize the MSAL app with the client credentials
#         app = msal.ConfidentialClientApplication(
#             self.client_id,
#             authority=authority_url,
#             client_credential=self.client_secret
#         )
#         # Acquire a token for the client
#         result = app.acquire_token_for_client(scopes)

#         # Check if the access token was successfully acquired
#         if 'access_token' in result:
#             return result['access_token']
#         else:
#             # If the access token was not acquired, print the error details and raise an exception
#             print('Failed to acquire token:', result.get('error'), result.get('error_description'))
#             raise Exception('Failed to acquire access token')
        
#     def send_email_notification(self, subject, body, emails):
#         """
#         This function sends an HTML email to the specified recipients.
#         """
#         endpoint = GRAPH_ENDPOINT + self.user_id + '/sendMail'
#         access_token = self.get_access_token()
    
#         email_msg = {
#             "Message": {
#                 "Subject": subject,
#                 "Body": {
#                     "ContentType": "HTML",
#                     "Content": body
#                 },
#                 "ToRecipients": [
#                     {
#                         "EmailAddress": {
#                             "Address": user
#                         }
#                     } for user in emails
#                 ]
#             },
#             "SaveToSentItems": "true"
#         }
    
#         response = requests.post(endpoint, headers={'Authorization': 'Bearer ' + access_token}, json=email_msg)
#         if response.ok:
#             print('Sent email successfully')
#         else:
#             raise Exception(f'Failed to send email: {response.status_code} - {response.json()}')

class Outlook:
    def __init__(self):
        """
        This method initializes the Outlook class with the client credentials.
        """
        credentials = get_secret(os.environ['ENV'] + '-email_credentials')
        self.config = credentials['aria_cloud']
        
        self.username = self.config['USERNAME_REPORTER']
        self.password = self.config['PASS_REPORTER_EMAIL']
        self.smtp = self.config['SMTP']
        self.port = self.config['PORT']


    def send_email_notification(self, subject, body, emails):
        message = MIMEMultipart()
        message['From'] = emails['sender']
        message['To'] = ", ".join(emails['recipients'])
        message['Subject'] = subject
        message.attach(MIMEText(body, 'html'))

        try:
            server = smtplib.SMTP(self.smtp, self.port)
            server.starttls() 

            server.login(self.username, self.password)

            text = message.as_string()
            server.sendmail(emails['sender'], emails['recipients'] + emails['bcc'], text)
            print('Email sent successfully')
        except Exception as e:
            print(f"Error: {e}")
            
        finally:
            server.quit()

        
    def generate_email_body_html(self, processed_workitems):
        env = os.environ.get('ENV')
        timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')

        body = """
        <html>
        <body>
            <p>Hello,</p>
            <p>Please find below the detailed report of the recent work item error in the ARIA system:</p>
            <hr>
        """

        for app_name, items in processed_workitems.items():
            if items:
                body += f"""
                <h3>{app_name} - Work Items Processing Summary</h3>
                <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f2f2f2;">
                        <th>WorkItem ID</th>
                        <th>Status</th>
                    </tr>
                """
                for item in items:
                    body += f"""
                    <tr>
                        <td>{item['id']}</td>
                        <td>{item['status']}</td>
                    </tr>
                    """
                body += "</table><br>"
        

        body += f"""
            </table>
            <p><strong>Environment:</strong> {env}</p>
            <p><strong>Execution Timestamp:</strong> {timestamp}</p>
            <hr>
            <p>If you need further information or if there are any discrepancies, please refer to the system logs for more details or contact the support team.</p>
            <p>Thank you,<br>The ARIA System Team</p>
        </body>
        </html>
        """
        return body
    
    