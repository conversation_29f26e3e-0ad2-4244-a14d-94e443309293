{"StartAt": "<PERSON><PERSON><PERSON><PERSON>", "States": {"TitleLoader": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-title_sftp_to_s3_to_aria", "ResultPath": "$.result", "Next": "ReportLoadedData"}, "ReportLoadedData": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-report_loaded_data", "Parameters": {"collection": "title"}, "Next": "WaitToProcessAllItems"}, "WaitToProcessAllItems": {"Type": "Wait", "Seconds": 600, "Next": "LoadingStuckWiReport"}, "LoadingStuckWiReport": {"Type": "Task", "Resource": "arn:aws:lambda:##REGION##:##ACCOUNT_ID##:function:##ENV##-loading_wi_report", "Parameters": {"app": "title", "stage": "post-inventory"}, "End": true}}}