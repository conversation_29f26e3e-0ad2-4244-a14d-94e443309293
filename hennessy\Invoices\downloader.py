import os
import sys
import time
import boto3
from datetime               import datetime
from botocore.exceptions    import ClientError
from invokeRPA.Utilities.boto3_utils import Boto3Utils

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import invokeRPA
from invokeRPA.Utilities.outlook_utils  import Outlook
from Invoices.config                    import DefaultConfig
import json
import traceback
class Downloader():
    def __init__(self, store, current_flow):
        self.store = store
        self.current_flow = current_flow
        self.__create_s3_path()

        # Enable log
        self.logger = invokeRPA.Logging(self.bucket, self.store)
        # Automation application
        self.chrome = invokeRPA.ChromeBrowserSelenium(self.logger, self.store)
        # Launch browser
        if not self.chrome.launch_browser(headless=True, store=store):
            # Raise exception if browser fails to launch
            raise Exception('Failed to launch browser')
        # Utilities objects
        self.utilities = invokeRPA.Utilities()
        # Boto3 objects
        self.boto3_utils = Boto3Utils()
        # Downloaded Invoices path
        self.destination_path = os.getcwd() + "/downloaded_invoices"
        # Assert folder exists
        if not os.path.exists(self.destination_path):
            os.makedirs(self.destination_path)

        self.s3 = boto3.client('s3')
        self.stored = False

        self.tracker = {}

    def __create_s3_path(self):
        self.bucket = DefaultConfig.get_s3_bucket()
        if self.current_flow == "post-inventory":
            folder = 'invoices'
        elif self.current_flow == "pre-inventory":
            folder = 'invoices_new_cars'
        else:
            folder = ""
        date_today = datetime.now().strftime('%Y/%m/%d')
        self.s3_path = f'{folder}/{date_today}/'

    def __get_error_name(self):
        return f'{os.getcwd()}/logs/{self.label}_{self.vin}_{self.stage.lower()}_error.png'
    
    def _init_outlook(self, stage = None):
        email_config = DefaultConfig.get_email_config(stage)
        self.outlook_user_id = email_config['USERID']
        self.outlook = Outlook(
            client_id=email_config['CLIENT_ID'],
            client_secret=email_config['CLIENT_SECRET'],
            tenant_id=email_config['TENANT_ID'],
            user_id=email_config['USERID']
        )

    def _log_info(self, message):
        self.logger.log(f'{self.label.upper()} - {self.stage}.{self.vin} - {message}')

    def _log_error(self, message):
        self.logger.error(f'{self.label.upper()} - {self.stage}.{self.vin} - {message}')
    
    def _handle_error(self, e):
        self._log_error(f'Failed: {str(e)}')
        screenshot_name = self.__get_error_name()
        if self.chrome.take_screenshot(screenshot_name):
            self.logger.error(f"Storing screenshot in '{screenshot_name}'")
            s3_path = f"logs/screenshots/{self.label}_{self.vin}_{self.stage.lower()}_error.png"
            self.s3.upload_file(screenshot_name, self.bucket, s3_path)
        else:
            self.logger.error(f"Failed to store screenshot in '{screenshot_name}'")
        raise e
    
    def __get_latest_file(self, directory, extension=".pdf"):
        """Returns the most recently modified file in the given directory"""
        try:
            files = [os.path.join(directory, f) for f in os.listdir(directory) if f.lower().endswith(extension)]
            self.logger.debug(f'Files in directory: {files}')
            files = [f for f in files if os.path.isfile(f)]
            return max(files, key=os.path.getctime) if files else None
        except Exception as e:
            self.logger.error(f'Error in get_latest_file: {e}')
            return None

    def __is_download_complete(self, file):
        """Check if the file is completely downloaded by checking its size"""
        if not os.path.exists(file):
            return False  # File disappeared
        initial_size = os.path.getsize(file)
        time.sleep(2)  # Wait a bit longer
        if not os.path.exists(file):
            return False  # File disappeared
        final_size = os.path.getsize(file)
        return initial_size == final_size

    def __is_download_in_progress(self, directory):
        """Check for partial downloads (Chrome: .crdownload, Firefox: .part, Edge: .download)"""
        partial_exts = [".crdownload", ".part", ".download", ".tmp"]
        try:
            files = os.listdir(directory)
            self.logger.debug(f'Checking for partial downloads in {directory}: {files}')
            return any(any(f.endswith(ext) for ext in partial_exts) for f in files)
        except Exception as e:
            self.logger.error(f'Error checking download progress: {e}')
            return False


    def get_file_from_download(self, extension=".pdf", name=None, s3_path=None, stage='SAVE_INVOICE'):
        """Get the file from the download folder and relocate it"""
        self.stage = stage
        download_path = os.getcwd() + "/" + self.store

        latest_file = None
        try:
            # Wait for the download to complete
            timeout = 60
            start_time = time.time()

            while time.time() - start_time < timeout:
                if not self.__is_download_in_progress(download_path):
                    latest_file = self.__get_latest_file(download_path, extension=extension)
                    if latest_file and self.__is_download_complete(latest_file):
                        break
                time.sleep(2)

            if not latest_file:
                self._log_error(f'Failed: File not downloaded or missing')
                return False

            # Rename the file to match the VIN number
            self._log_info(f'Moving file {latest_file} to the correct location')
            if name:
                new_file_path = os.path.join(self.destination_path, f"{name}{extension}")
            else:
                new_file_path = os.path.join(self.destination_path, f"{self.vin}{extension}")

            # Ensure no filename collision
            try:
                if os.path.exists(new_file_path):
                    self._log_error(f'File {new_file_path} already exists. Overwriting.')
                    os.remove(new_file_path)
                os.rename(latest_file, new_file_path)
            except Exception as e:
                self._log_error(f'Error renaming file: {e}')
                return False

            # Post to S3
            try:
                self.upload_to_s3(new_file_path, s3_path=s3_path, filename=name, extension=extension)
            except Exception as e:
                self._log_error(f'Error uploading to S3: {e}')
                return False
            
            self.stored = True

            if self.stored:
                self._log_info(f'Successfully uploaded {new_file_path} to S3')
                return True

        except Exception as e:
            # Clean up the file if it exists
            if latest_file and os.path.exists(latest_file):
                try:
                    os.remove(latest_file)
                except Exception as e:
                    self._log_error(f'Error deleting file: {e}')
            # Log the error
            self._handle_error(e)

        return False


    def upload_to_s3(self, file_path, s3_path=None, filename=None, extension=".pdf"):
        """Upload file to S3"""
        self.stage = 'UPLOAD_TO_S3'
        try:
            self._log_info('Start')
            key = (self.s3_path if not s3_path else s3_path) + \
                (filename if filename else self.vin) + extension
            self.s3.upload_file(file_path, self.bucket, key)
            destination = f's3://{self.bucket}/{key}'
            self._log_info(f'Success: {destination}')
            return destination
        except ClientError as e:
            self._handle_error(e)


    def save_invoice(self):
        """Download invoice"""
        pass

    def search_by_vin(self, vin, count):
        """Search invoice"""
        pass
    
    def reset_password(self, user, actual_password, new_password):
        pass

    def reset_password_and_refresh_secret(self, secret_name, user, actual_password, new_password):
        print("MIRA LA NEW PASSWORD", new_password)
        successfully_changed = self.reset_password(user, actual_password, new_password)
        
        if successfully_changed:
            self.boto3_utils.update_secret(secret_name, json.dumps({'user': user, 'old_password': actual_password, 'actual_password': new_password}))
            
        return successfully_changed
            
    def download_vins(self, vins):
        count = 1
        self.logger.log('Downloading invoices...')
        for v in vins:
            self.tracker[v] = {'error': None, 'stored': False, 'stage': None}
            try:
                self.logger.log(f'Downlading invoice for VIN: {v}')
                found = self.search_by_vin(v, count)
                # Only increment count if the search was successful
                count += 1

                if not found:
                    self.tracker[v]['error'] = 'VIN not found'
                    self.tracker[v]['stage'] = self.stage
                    continue
                
                self.save_invoice()
                if self.stored:
                    self.tracker[v]['stored'] = self.stored
                else:
                    self.tracker[v]['error'] = 'Failed to store invoice'
                    self.tracker[v]['stage'] = self.stage
            except Exception as e:
                self.logger.error(f'Download invoices failed for VIN {v}: {str(e)}')
                self.tracker[v]['error'] = str(e)
                self.tracker[v]['stage'] = self.stage

        try:
            del self.chrome
        except Exception as e:
            self.logger.error(f'Failed to close browser: {str(e)}')

        return self.tracker
    
    def download_new_vehicle_report(self, store):
        pass

    def download_new_vehicles_report(self, store):
        self.logger.log('Downloading new vehicle report...')
        self.tracker = {'error': None, 'stored': False, 'stage': None}
        try:
            self.logger.log(f'Downlading new vehicles report')

            found = self.download_new_vehicle_report(store)
            if not found:
                self.tracker['error'] = 'VIN not found'
                self.tracker['stage'] = self.stage
                    
            if self.stored:
                self.tracker['stored'] = self.stored
            else:
                self.tracker['error'] = 'Failed to store invoice'
                self.tracker['stage'] = self.stage
        except Exception as e:
            self.logger.error(f'Download report failed: {str(e)}')
            self.tracker['error'] = str(e)
            self.tracker['stage'] = self.stage

        try:
            del self.chrome
        except Exception as e:
            self.logger.error(f'Failed to close browser: {str(e)}')

        return self.tracker