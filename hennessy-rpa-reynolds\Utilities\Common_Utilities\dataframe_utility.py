import pandas as pd
from Utilities.Common_Utilities.logger_utility import Logger
import traceback

class Dataframe():

    def __init__(self, log_path):
        self.l = Logger(log_path)

    #Conver column to number
    #   INPUT
    #       -dataframe
    #       -column_name
    #   OUTPUT
    #       -dataframe  
    def convert_column_to_number(self, dataframe, column_name):
        try:
            dataframe[column_name] = pd.to_numeric(dataframe[column_name])
            return dataframe
        except Exception as e:           
            raise Exception ('Exception occurred on convert_column_to_number method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Concatenate dataframes
    #   INPUT
    #       -dataframes: array of dataframes
    #       -ignore_index_flag
    #   OUTPUT
    #       -concatenated_dataframe    
    def concatenate_dataframes(self, dataframes_array, ignore_index_flag=True):
        try:
            concatenated_dataframe = pd.concat(dataframes_array, ignore_index=ignore_index_flag)
            return concatenated_dataframe
        except Exception as e:           
            raise Exception ('Exception occurred on concatenate_dataframes method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Get range from dataframe
    #   INPUT
    #       -dataframe
    #       -start_row
    #       -end_row
    #   OUTPUT
    #       -dataframe 
    def get_range_from_dataframe(self, dataframe, start_row, end_row):
        try:
            dataframe = dataframe.iloc[start_row:end_row].copy()
            return dataframe
        except Exception as e:           
            raise Exception ('Exception occurred on get_range_from_dataframe method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Get row values from dataframe
    #   INPUT
    #       -dataframe
    #       -start_row
    #       -end_row
    #   OUTPUT
    #       -values 
    def get_row_values_from_dataframe(self, dataframe, row):
        try:
            values = dataframe.iloc[row].values
            return values
        except Exception as e:           
            raise Exception ('Exception occurred on get_row_values_from_dataframe method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
    
    #Add column to dataframe
    #   INPUT 
    #       -dataframe
    #       -position
    #       -column_name
    #       -value_to_enter
    #   OUTPUT
    #       -dataframe
    def add_column_to_dataframe(self, dataframe, position, column_name, value_to_enter):
        try:
            dataframe.insert(loc = position, column = column_name, value = value_to_enter)
            return dataframe
        except Exception as e:           
            raise Exception ('Exception occurred on add_column_to_dataframe method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))       
    