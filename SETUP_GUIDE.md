# 🛠️ Detailed Setup Guide - Hennessy Invoice Processing System

## 📋 Prerequisites Checklist

### **System Requirements:**
- [ ] Windows 10/11, macOS, or Linux
- [ ] Python 3.11+ installed
- [ ] Node.js 16+ installed  
- [ ] Docker Desktop installed
- [ ] Git installed
- [ ] 8GB+ RAM recommended

### **Accounts & Access:**
- [ ] AWS Account with appropriate permissions
- [ ] MongoDB Atlas account (or local MongoDB)
- [ ] OpenAI API account (optional)
- [ ] Vendor portal credentials for each brand

## 🔧 Step-by-Step Installation

### **Step 1: Clone and Setup Project**

```bash
# Clone the repository
git clone <your-repo-url>
cd Invoke

# Verify project structure
ls -la
# Should see: hennessy/, hennessy-aria/, hennessy-rpa-reynolds/
```

### **Step 2: Install Global Dependencies**

```bash
# Install Serverless Framework v3 (IMPORTANT: Use version 3)
npm install -g serverless@3

# Verify installation
serverless --version
# Should show: Framework Core: 3.x.x

# Install LocalStack for local development
pip install localstack

# Install AWS CLI (if not installed)
# Windows: Download from AWS website
# macOS: brew install awscli
# Linux: sudo apt-get install awscli
```

### **Step 3: Configure AWS**

```bash
# Configure AWS credentials
aws configure

# Enter when prompted:
# AWS Access Key ID: [Your Access Key]
# AWS Secret Access Key: [Your Secret Key]  
# Default region name: us-east-1
# Default output format: json

# Test AWS connection
aws sts get-caller-identity
```

### **Step 4: Setup Hennessy (Main RPA Service)**

```bash
cd hennessy

# Create virtual environment (recommended)
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env  # If exists, or create manually

# Edit .env file with your configurations:
# MONGO_URI=mongodb://localhost:27017/hennessy
# AWS_REGION=us-east-1
# LOG_LEVEL=INFO
```

### **Step 5: Setup Hennessy-Aria (Serverless Functions)**

```bash
cd ../hennessy-aria

# Install Node.js dependencies
npm install

# Verify serverless plugins
serverless plugin list

# Should show installed plugins:
# - serverless-deployment-bucket
# - serverless-hooks  
# - serverless-localstack
# - serverless-offline
# - serverless-step-functions
```

### **Step 6: Setup Local Development Environment**

```bash
# Start LocalStack (in a separate terminal)
localstack start

# Wait for LocalStack to fully start (check logs)
# You should see: "Ready."

# Create local S3 bucket
aws s3 mb s3://ach-deployment-bucket-local --endpoint-url=http://localhost:4566

# Verify bucket creation
aws --endpoint-url=http://localhost:4566 s3 ls
```

### **Step 7: Configure Vendor Credentials**

Create a `secrets.json` file for local development:

```json
{
  "ford": {
    "username": "your-ford-username",
    "password": "your-ford-password"
  },
  "lexus": {
    "username": "your-lexus-username", 
    "password": "your-lexus-password"
  },
  "jlr": {
    "username": "your-jlr-username",
    "password": "your-jlr-password"
  },
  "porsche": {
    "username": "your-porsche-username",
    "password": "your-porsche-password"
  },
  "cadillac": {
    "username": "your-cadillac-username",
    "password": "your-cadillac-password"
  }
}
```

### **Step 8: Setup MongoDB**

#### **Option A: Local MongoDB**
```bash
# Install MongoDB locally
# Windows: Download from MongoDB website
# macOS: brew install mongodb-community
# Linux: sudo apt-get install mongodb

# Start MongoDB
mongod --dbpath /path/to/your/db

# Create database
mongo
> use hennessy_dev
> db.createCollection("folios")
> exit
```

#### **Option B: MongoDB Atlas (Cloud)**
1. Go to https://cloud.mongodb.com
2. Create free cluster
3. Get connection string
4. Update MONGO_URI in your environment

### **Step 9: Deploy to Local Environment**

```bash
cd hennessy-aria

# Deploy all functions to LocalStack
npm run deploy:local

# Check deployed functions
aws --endpoint-url=http://localhost:4566 lambda list-functions

# Should show functions like:
# - local-bre_handler
# - local-llm_extractor
# - local-pdf_utils
# etc.
```

## 🧪 Testing Your Setup

### **Test 1: Basic Lambda Function**

```bash
# Test a simple function
serverless invoke --stage local --function bre_handler --data '{
  "body": {
    "test": true
  }
}'

# Expected: Success response (not error)
```

### **Test 2: Invoice Download (Dry Run)**

```python
# Create test file: test_download.py
import sys
sys.path.append('./hennessy')
from app import lambda_handler

# Test event (use test VINs)
test_event = {
    "store": "FOR",
    "vins": ["TEST123456789"],
    "action": "invoice_download"
}

# Run test (will fail at login but should reach that point)
try:
    result = lambda_handler(test_event, None)
    print("Test passed - reached vendor login")
except Exception as e:
    print(f"Expected error at login: {e}")
```

### **Test 3: Document Processing**

```bash
# Test PDF processing function
serverless invoke --stage local --function pdf_utils --data '{
  "action": "process_document",
  "test_mode": true
}'
```

## 🚀 Production Deployment

### **Step 1: Setup AWS Secrets Manager**

```bash
# Create secrets for each vendor
aws secretsmanager create-secret \
  --name "snd-hen-ford-credentials" \
  --description "Ford portal credentials" \
  --secret-string '{"username":"your-username","password":"your-password"}'

# Repeat for other vendors:
# - snd-hen-lexus-credentials
# - snd-hen-jlr-credentials  
# - snd-hen-porsche-credentials
# - snd-hen-cadillac-credentials
```

### **Step 2: Deploy to Staging**

```bash
# Deploy to staging environment
npm run deploy:snd

# Monitor deployment
aws cloudformation describe-stacks --stack-name hennesy-snd-hen
```

### **Step 3: Deploy to Production**

```bash
# Deploy to production
npm run deploy:prd

# Verify deployment
aws lambda list-functions --region us-east-1 | grep prd-hen
```

## 🔍 Troubleshooting

### **Common Issues:**

#### **1. Serverless Version Error**
```bash
# Error: Serverless version not supported
# Solution: Ensure you're using Serverless v3
npm uninstall -g serverless
npm install -g serverless@3
```

#### **2. LocalStack Connection Issues**
```bash
# Error: Cannot connect to LocalStack
# Solution: Check if LocalStack is running
docker ps | grep localstack

# Restart if needed
localstack stop
localstack start
```

#### **3. AWS Permissions Error**
```bash
# Error: Access denied
# Solution: Check IAM permissions
aws iam get-user
aws iam list-attached-user-policies --user-name your-username
```

#### **4. MongoDB Connection Error**
```bash
# Error: Cannot connect to MongoDB
# Solution: Check connection string and network access
mongo "your-connection-string"
```

### **Debugging Commands:**

```bash
# Check LocalStack services
curl http://localhost:4566/health

# View Lambda logs
aws --endpoint-url=http://localhost:4566 logs describe-log-groups

# Test S3 connectivity  
aws --endpoint-url=http://localhost:4566 s3 ls

# Check deployed CloudFormation stacks
aws --endpoint-url=http://localhost:4566 cloudformation list-stacks
```

## 📊 Monitoring & Logs

### **Local Development:**
- LocalStack Dashboard: https://app.localstack.cloud/dashboard
- Function logs: Check terminal where LocalStack is running
- MongoDB logs: Check MongoDB terminal/service

### **Production:**
- AWS CloudWatch: Monitor Lambda function logs
- AWS X-Ray: Trace function execution
- MongoDB Atlas: Monitor database performance

## 🔄 Next Steps After Setup

1. **Configure vendor-specific settings** in each vendor module
2. **Set up scheduled jobs** using AWS EventBridge
3. **Configure email processing** rules
4. **Set up monitoring alerts** in CloudWatch
5. **Test with real VINs** (start with small batches)
6. **Configure Aria ERP integration** endpoints
7. **Set up backup and disaster recovery** procedures

## 📞 Getting Help

If you encounter issues:
1. Check the logs first (LocalStack terminal or CloudWatch)
2. Verify all prerequisites are installed correctly
3. Ensure AWS credentials have proper permissions
4. Check MongoDB connectivity
5. Verify vendor credentials are correct

Remember: Start with local development using LocalStack before deploying to AWS!
