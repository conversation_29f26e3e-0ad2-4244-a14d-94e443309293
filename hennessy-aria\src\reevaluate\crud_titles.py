# This module contains a class for interacting with the Folios collection in the MongoDB database.

import os
from datetime import datetime
from mongo_utils import Mongo
from boto3_utils import get_secret


class CrudTitles:
    def __init__(self, mongo):
        self.mongo = mongo
        self.collection_name = 'title'

    def insert_title(self, uuid, concated_title, images, title_file, aria_wi_id):
        """
        This function inserts a folio into the database.
        """

        title_document = {
            "title_id": str(uuid),
            "images_of_title": images,
            "path": concated_title,
            "from_file": title_file,
            "aria_wi_id": aria_wi_id,
            "read_at": datetime.now(),
            "status_history": ["Pending"]
        }

        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        self.mongo.insert_one(title_document)

    def find_title_using_vin(self, vin):
        """
        This function finds a title using a VIN.
        """ 
        self.mongo.select_db_and_collection(db_name=os.environ['MONGO_DATABASE'], collection_name=self.collection_name)
        return self.mongo.find({"extracted_data.title.vin": vin})
    
    def __del__(self):
        self.mongo.close_connection()