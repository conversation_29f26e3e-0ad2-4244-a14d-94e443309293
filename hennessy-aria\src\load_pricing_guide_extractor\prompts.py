prompt = "You are the lead data analyst working on taking unstructured car pricing guides and returning a structured JSON output of extracted fields."

message_get_model_codes = """
    Im going to give you the plain text OCR of a pricing guide. I need you to return all the model codes that there are in the text. The
    model codes are a combination of characters and numbers.
    
    JSON Structure:
    Ensure the output strictly adheres to this template:
    {
        "model_codes": [
            model_code_1,
            model_code_2,
            model_code_3,
            ...
            model_code_n
        ]
    }

    Example Reference:
    Use this as a model to ensure accurate formatting:
    {
        "model_codes": [
            "ABCDEF1",
            "ADSFASDF",
            "GDSFG2",
            ...
            "ASDFADSF2"
        ]
    }

    Additional Guidelines:
        * Consistency and Precision: Ensure consistency and precision in the output.
        * No Additional Text: The output should strictly follow the template with no extra text or metadata.
        * There are values that they are not model codes as the pdf has watermark with a 6 digit code.

    ***** DONT MISS ANY MODEL CODE AS ITS VERY IMPORTANT TO HAVE THEM ALL *****
    ***** USE THE OCR OF THE DOCUMENT TO EXTRACT ALL THE MODEL CODES AND ITS VALUES TO RECREATE THE TEMPLATE JSON *****

    The ocr is the next: 

"""
column_names = {
    "HON": {
        "FIELD_A": 'DMA',
        "FIELD_B": "FPA",
        "FIELD_C": "FTF",
        "FIELD_D": "HTB"
    },
    "ACU": {
        "FIELD_A": 'CMA',
        "FIELD_B": "FPA",
        "FIELD_C": "FTF",
        "FIELD_D": "HB"
    }
}

message_extraction = """
    Use the OCR data extracted from the provided image of a Honda pricing guide and convert it into a structured JSON format. Follow these detailed instructions:

    Exclusion Criteria:
        Exclude Blue Text: Omit any lines identified as containing blue text. If your tool can recognize color, utilize it. Otherwise, use discernible patterns or markers indicative of blue text.
    Data Grouping:
        New Entry Start: Begin a new JSON entry when a "Model Code" appears.
        Model codes will be grouped by Model Names.
        Missing "Model Code": If a line lacks a "Model Code", apply the last known "Model Code" from the preceding entry.
    Value Formatting:
        Monetary Values: Convert all monetary values to floats, excluding currency symbols (e.g., transform "$34,350.00" into 34350.00).

    Take in mind that most of the model codes will have 2 entries.
    
    JSON Structure:
    Ensure the output strictly adheres to this template:
    {
        "Model Code 1": [
            {
                "Model name": val_model_name,
                "MSRP": val_msrp,
                "Dlr Inv.": val_dlr_inv,
                "Model description": val_model_description,
                "FIELD_A": val_dma,
                "FIELD_B": val_fpa,
                "FIELD_C": val_ftf,
                "FIELD_D": val_htb
            },
            {
                "Model name": val_model_name,
                "MSRP": val_msrp_2,
                "Dlr Inv.": val_dlr_inv_2,
                "Model description": val_model_description_2,
                "FIELD_A": val_dma_2,
                "FIELD_B": val_fpa_2,
                "FIELD_C": val_ftf_2,
                "FIELD_D": val_htb_2
            }
        ],

        "Model Code 2": [
            {
                "Model name": val_model_name_2,
                "MSRP": val_msrp_3,
                "Dlr Inv.": val_dlr_inv_3,
                "Model description": val_model_description_3,
                "FIELD_A": val_dma_3,
                "FIELD_B": val_fpa_3,
                "FIELD_C": val_ftf_3,
                "FIELD_D": val_htb_3
            },
            {
                "Model name": val_model_name_2,
                "MSRP": val_msrp_4,
                "Dlr Inv.": val_dlr_inv_4,
                "Model description": val_model_description_4,
                "FIELD_A": val_dma_4,
                "FIELD_B": val_fpa_4,
                "FIELD_C": val_ftf_4,
                "FIELD_D": val_htb_4
            }
        ],

        ...

        "Model Code N": [
            {
                "Model name": val_model_name_n,
                "MSRP": val_msrp_n,
                "Dlr Inv.": val_dlr_inv_n,
                "Model description": val_model_description_n,
                "FIELD_A": val_dma_n,
                "FIELD_B": val_fpa_n,
                "FIELD_C": val_ftf_n,
                "FIELD_D": val_htb_n
            },
            {
                "Model name": val_model_name_n,
                "MSRP": val_msrp_n_2,
                "Dlr Inv.": val_dlr_inv_n_2,
                "Model description": val_model_description_n_2,
                "FIELD_A": val_dma_n_2,
                "FIELD_B": val_fpa_n_2,
                "FIELD_C": val_ftf_n_2,
                "FIELD_D": val_htb_n_2
            }
        ]
    }
    Example Reference:
    Use this as a model to ensure accurate formatting:

    {
        "RS5H5SJXW": [
            {
                "Model name": "CR-V FHEV",
                "MSRP": 34350.00,
                "Dlr Inv.": 32735.55,
                "Model description": "2AW 5D 2.0 HYBRID SPORT CONTINUOUSLY VARIABLE TRANSMISSION",
                "FIELD_A": 150.00,
                "FIELD_B": 343.00,
                "FIELD_C": 50.00,
                "FIELD_D": 515.25
            },
            {
                "Model name": "CR-V FHEV",
                "MSRP": 1234.13,
                "Dlr Inv.": 142432.67,
                "Model description": "ANOTHER DESCRIPTION EXAMPLE OF A MODEL",
                "FIELD_A": 24112.43,
                "FIELD_B": 14212.56,
                "FIELD_C": 4123.12,
                "FIELD_D": 15141.11
            }
        ],
        ...
    }
    
    Additional Guidelines:
        * Consistency and Precision: Ensure consistency and precision in the output.
        * No Additional Text: The output should strictly follow the template with no extra text or metadata.

    ***** DONT MISS ANY MODEL CODE AS ITS VERY IMPORTANT TO HAVE THEM ALL *****
    ***** USE THE OCR OF THE DOCUMENT TO EXTRACT ALL THE MODEL CODES AND ITS VALUES TO RECREATE THE TEMPLATE JSON *****

"""