import os
import json
import requests
from typing import Dict, Any, Optional
from datetime import datetime

class APIClient:
    def __init__(self, api_endpoint: str, api_token: str):
        if not api_endpoint:
            raise ValueError("API_ENDPOINT must be provided.")
        if not api_token:
            raise ValueError("API_TOKEN must be provided.")
        
        self.api_endpoint = api_endpoint
        self.api_token = api_token

    def get_headers(self) -> Dict[str, str]:
        """Returns headers for API requests."""
        return {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }

    def make_request(self, action: str, data: Dict[str, Any] = None, secret_data: Dict[str, Any] = None, database: Optional[str] = None, collection: Optional[str] = None, filter_data: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Generic function to make API requests.
        
        :param action: The action to perform (e.g., 'get_one', 'update_one', 'get_many').
        :param data: The general data payload (for queue operations).
        :param secret_data: The secret-specific data payload.
        :param database: Database name.
        :param collection: Collection name.
        :param filter_data: Optional filter criteria for database queries.
        :return: JSON response or None if an error occurs.
        """
        if secret_data:
            endpoint = f"{self.api_endpoint}/secrets_handler"
        elif data:
            endpoint = f"{self.api_endpoint}/queues_handler"
        else:
            endpoint = self.api_endpoint

        payload = {"action": action}
        
        if data:
            payload["data"] = data
        if secret_data:
            payload["secret_data"] = secret_data
        if database:
            payload["database_name"] = database
        if collection:
            payload["collection_name"] = collection
        if filter_data:
            payload["filter"] = filter_data

        try:
            response = requests.post(endpoint, json=payload, headers=self.get_headers())
            response.raise_for_status()  # Raise an error for non-2xx responses
            print(f"✅ Success: {action}")
            try:
                response = response.json()
            except:
                response = json.loads(response.text.replace("\'", "\""))
            return response
        
        except requests.exceptions.RequestException as e:
            print(f"⚠️ Request failed: {e}")
            return None
    
    def get_secret(self, secret_name: str) -> Optional[Dict[str, Any]]:
        return self.make_request("get", secret_data={secret_name: ""})

    def update_secret(self, secret_name: str, user: str, old_pass: str, actual_pass: str) -> Optional[Dict[str, Any]]:
        return self.make_request("update", secret_data={secret_name: {"user": user, "old_password": old_pass, "actual_password": actual_pass}})
    
    def get_cars_to_stock_in_post_inventory(self, db_name) -> Optional[Dict[str, Any]]:
        return self.make_request("get_many", data={"$or": [{"status": 7}, {"status": 9}], "current_flow": "post-inventory"}, database=db_name, collection="vin")

    def get_cars_to_stock_in_used_cars(self, db_name) -> Optional[Dict[str, Any]]:
        return self.make_request("get_many", data={"$or": [{"status": 7}, {"status": 9}], "current_flow": "used-cars"}, database=db_name, collection="vin")

    def get_cars_to_stock_in_pre_inventory(self, db_name) -> Optional[Dict[str, Any]]:
        return self.make_request("get_many", data={"$or": [{"status": 7}, {"status": 9}], "current_flow": "pre-inventory"}, database=db_name, collection="vin")

    def update_vin_status_in_mongo(self, vin: str, status: int, db_name) -> Optional[Dict[str, Any]]:
        return self.make_request("update_one", data={"$set": {"status": status}, "$push": {"status_history": status}}, database=db_name, collection="vin", filter_data={"vin": vin})
    
    def update_invoice_vin_status(self, vin: str, db_name, stage) -> Optional[Dict[str, Any]]:
        return self.make_request("update_one", data={
            "$set": {
                f"flows.{stage}.docs.invoice.aria_data.status": "Completed",
                f"flows.{stage}.docs.invoice.updated_at": datetime.now().isoformat()
            },
            "$push": {
                f"flows.{stage}.docs.invoice.aria_data.status_history": "Completed"
            }
        }, database=db_name, collection="vin", filter_data={"vin": vin})
    
    def get_completed_status_uuid(self, invoice_app_id, db_name) -> Optional[str]:
        response = self.make_request("get_one", data={"app_id": invoice_app_id}, database=db_name, collection="aria_status")
        
        if response:
            status_info = response[0].get("status", {})
            for k, v in status_info.items():
                if v.get("label") == "Completed":
                    return k
        return None
    
    def get_needs_status_uuid(self, invoice_app_id, db_name) -> Optional[str]:
        response = self.make_request("get_one", data={"app_id": invoice_app_id}, database=db_name, collection="aria_status")
        
        if response:
            status_info = response[0].get("status", {})
            for k, v in status_info.items():
                if v.get("label") == "Needs Attention":
                    return k
        return None
