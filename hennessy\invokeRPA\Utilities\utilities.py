import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import time

class Utilities:

    def __init__(self):
        pass

    def send_html_email(self, emailfrom, emailusername, emailhost, emailport, emailpassword, subject, body, mail_to, mail_cc, mail_bcc, ttls=False,
                        auth=False):
        # Create message container - the correct MIME type is multipart/alternative.
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = emailfrom
        msg['To'] = mail_to
        msg['CC'] = mail_cc
        msg['BCC'] = mail_bcc
        # Record the MIME types of both parts - text/plain and text/html.
        part = MIMEText(body, 'html')

        # Attach parts into message container.
        # According to RFC 2046, the last part of a multipart message, in this case
        # the HTML message, is best and preferred.
        msg.attach(part)

        email_address = mail_to.split(',') + mail_cc.split(',') + mail_bcc.split(',')
        # Send the message
        try:
            server = smtplib.SMTP(emailhost, emailport)
            server.ehlo()
            if ttls:
                server.starttls()
            if auth:
                server.login(emailusername, emailpassword)
            # sendmail function takes 3 arguments: sender's address, recipient's address
            # and message to send - here it is sent as one string.
            server.sendmail(emailfrom, email_address, msg.as_string())
            server.close()
            return 'sent'
        except Exception as ex:
            text = 'no, error : ' + str(ex)
            return text

    def sleep(self, seconds):
        time.sleep(seconds)

    def get_today(self, format_str="%Y-%m-%d"):
        return time.strftime(format_str)
