from Utilities.Common_Utilities.logger_utility import Logger
import win32com.client
import traceback

class Outlook():

    def __init__(self, log_path):
        self.l = Logger(log_path)
        self.outlook = win32com.client.Dispatch("Outlook.Application")

    # Send email
    #   INPUT:
    #       -to: recipient of email
    #       -subject: subject of email
    #       -body: body of email
    #       -attachment: attachment of email
    def send_email(self, to, subject, body, attachment):
        try:  
            mail = self.outlook.CreateItem(0)
            mail.To = to
            mail.Subject = subject
            mail.HTMLBody = body
            if attachment != '':
                mail.Attachments.Add(attachment)
            mail.Send()
        except Exception as e: 
            raise Exception ('Exception occurred on send_email method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    #Get all emails from outlook subfolder
    #   INPUT:
    #       -folder: Outlook subfolder where python will pick the emails
    #   OUTPUT:
    #       -messages: list of all the emails
    def extract_emails(self, folder, filter):
        try:
            outlook = self.outlook.GetNamespace("MAPI")
            inbox = outlook.GetDefaultFolder(6)
            if folder != '':
                inbox = inbox.Folders(folder)
            messages = inbox.Items
            if filter:
                messages = messages.Restrict(filter)
            return messages
        except Exception as e:
            print(str(e))           
            raise Exception ('Exception occurred on extract_emails method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    #Save message
    #   INPUT:
    #       -file_path
    def save_message_as(self, message, file_path):
        try:
            message.SaveAs(file_path)
        except Exception as e:           
            raise Exception ('Exception occurred on save_message_as method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))

    #Forward email
    #   INPUT:
    #       -mail_id: email id
    #       -forward_address: recipient of email
    #       -subject: subject of email
    #       -body: body at the beginning of the message
    def forward_email(self, mail_id, forward_address, subject, body):
        try:
            outlook = self.outlook.GetNamespace("MAPI")
            email = outlook.Session.GetItemFromID(mail_id)
            NewMsg = email.Forward()
            NewMsg.Subject = subject
            NewMsg.HTMLBody = body + email.HTMLBody
            NewMsg.To = forward_address
            NewMsg.Send()
        except Exception as e:            
            raise Exception('Exception occurred in forward_email method. Details: ' + str(e)+ '. More info: ' + str(traceback.format_exc()))

    #Mark as read
    #   INPUT:
    #       -mail_id: email id
    def mark_as_read(self, mail_id):
        try:
            email = self.outlook.Session.GetItemFromID(mail_id)
            email.Unread = False
            email.Save()
        except Exception as e:           
            raise Exception('Exception occurred in mark_as_read method. Details: ' + str(e)+ '. More info: ' + str(traceback.format_exc()))
        
    #Delete email
    #   INPUT:
    #       -mail_id: email id
    def delete_email(self, mail_id):
        try:
            email = self.outlook.Session.GetItemFromID(mail_id)
            email.Delete()
        except Exception as e:           
            raise Exception('Exception occurred in delete_email method. Details: ' + str(e)+ '. More info: ' + str(traceback.format_exc()))

    #Move email to specified subfolder
    #   INPUT:
    #       -mail_id: email id
    #       -subfolder: subfolder of inbox
    def move_email_subfolder(self, mail_id, subfolder):
        try:
            moved_email = ""
            outlook = self.outlook.GetNamespace("MAPI")
            donebox = outlook.GetDefaultFolder(6).Folders(subfolder)
            email = self.outlook.Session.GetItemFromID(mail_id)
            counter = 0
            while (True):
                try:
                    moved_email = email.move(donebox)
                    break
                except Exception as e:
                    counter += 1
                    if counter == 5:
                        raise Exception (str(e))
                    elif 'The operation failed' in str(e):
                        if getattr(moved_email, "EntryID", "error") != "error":
                            break                        

            if moved_email.EntryID != mail_id:
                self.l.log("INFO", "Email was moved")
            else:
                raise Exception("Email was not moved")
        except Exception as e:            
            raise Exception('Exception occurred in forward_email method. Details: ' + str(e)+ '. More info: ' + str(traceback.format_exc()))