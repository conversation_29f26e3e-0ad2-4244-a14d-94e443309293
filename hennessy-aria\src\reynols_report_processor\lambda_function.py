
from boto3_utils import get_latest_csv_metadata, download_csv_from_s3, download_file_from_s3
import os
import pandas as pd
from crud_report_rows import CrudReynolsReport
from crud_invoices import CrudInvoices
from sftp_utils import get_report_file
import json
from vininfo import Vin
import traceback

def map_store_val(val):
    if "LA" in val:
        return "LOA"
    elif "LG" in val:
        return "LOG"
    elif "PO" in val:
        return "POR"
    elif "NW" in val:
        return "PNW"
    elif "HO" in val:
        return "HON"
    elif val in ['L4', 'FT', 'FO']:
        return "FOR"
    elif "J1" in val:
        return "JLRB"
    elif "L1" in val:
        return "JLRB"
    elif "L2" in val:
        return "JLRN"
    elif "L3" in val:
        return "JLRG"
    elif val in ['CD', 'TK']:
        return "CAD"
    elif val in ['MA', 'GC']:
        return "MBG"
    
def map_store_val_used_cars(store, acctg):
    acctg = str(acctg)
    if "LOA" in store:
        if "1" in acctg:
            return "LOA"
        elif "2" in acctg:
            return "LOG"
    elif "POR" in store:
        if "1" in acctg:
            return "POR"
        elif "2" in acctg:
            return "PNW"
    if "JLR" in store:
        if "1" in acctg:
            return "JLRB"
        elif "2" in acctg:
            return "JLRN"
        elif "3" in acctg:
            return "JLRG"
        
    return store
    
    
    
def process_post_inventory_report(file_path, latest_file_path):

    crud_reynols_report = CrudReynolsReport()
    reynols_report_df = pd.read_csv(latest_file_path)

    for idx, row in reynols_report_df.iterrows():
        row['STORE'] = map_store_val(row['ACCTG-MK'])
        row['SVC RO DATE'] = row['RO-CLOSE-DATE'] if row['RO-CLOSE-DATE'] != "" or row['RO-CLOSE-DATE'].replace(" ", "") != "" else row['SVC RO DATE']
        existing_row = crud_reynols_report.find_report_row_by_vin(row['VIN'])
        # Avoid inserting those VINs starting with AM cause are sold vehicles
        if existing_row is None and row['VIN'][:2] != "AM":
            print(" Inserting in mongo VIN: ", row['VIN'])
            crud_reynols_report.insert_row(row, file_path)
        else:
            print(" Updating in mongo VIN: ", row['VIN'])
            crud_reynols_report.update_row_post_inventory(row, file_path, row['VIN'])


def process_used_cars_report(file_path, latest_file_path):
    
    crud_reynols_report = CrudReynolsReport()
    crud_invoices = CrudInvoices()
    reynols_report_df = pd.read_csv(latest_file_path)
    not_loaded_vins = []

    for idx, row in reynols_report_df.iterrows():
        store = row["STORE"]
        acctg = row["ACCTG-MK"]
        row['STORE'] = map_store_val_used_cars(store, acctg)

        existing_row = crud_reynols_report.find_report_row_by_vin(row['VIN'])

        vin_val = str(row['VIN'])
        if (not vin_val or vin_val == '' or len(vin_val) != 17) or Vin(vin_val).verify_checksum() == False:
            not_loaded_vins.append(vin_val)
            continue

        if existing_row is None:
            print(" Inserting in mongo VIN: ", vin_val)
            invoice_created_data = crud_invoices.find_invoice_used_car_by_vin_and_not_discarded(vin_val)
            if invoice_created_data is not None:
                crud_reynols_report.insert_row_used_car_with_metadata(row, file_path, invoice_created_data)
            else:
                crud_reynols_report.insert_row_used_car(row, file_path)
        else:
            print(" Updating in mongo VIN: ", vin_val)
            crud_reynols_report.update_row_used_car(row, file_path, vin_val)

def lambda_handler(event, context):

    try:

        stage = event.get("stage", None)
        if stage is None or stage == "":
            return {
                'statusCode': 500,
                'body': {
                    "message": json.dumps(f"Error no stage provide!")
                }
            }
        
        print(" ****** DOWNLOADING REPORT ****** ")

        path_save_report = ""
        sftp_files_path = ""
        report_name_start_with = ""
        if stage == 'post-inventory':
            path_save_report = os.environ['REYNOLS_REPORT_FOLDER']
            sftp_files_path = os.environ['SFTP_FILES_PATH']
            report_name_start_with = "NC_"
        elif stage == 'pre-inventory':
            path_save_report = os.environ['PRE_INVENTORY_REPORT_FOLDER']
            sftp_files_path = os.environ['SFTP_FILES_PATH_PRE_INVENTORY']
            report_name_start_with = ""
        elif stage == 'used-cars':
            path_save_report = os.environ['USED_CARS_REPORT_FOLDER']
            sftp_files_path = os.environ['SFTP_FILES_PATH_USED_CARS']
            report_name_start_with = "UC_"
        
        filename = get_report_file(bucket_name=os.environ['BUCKET'], folder_name=path_save_report, remote_folder_path=sftp_files_path, report_name_start_with=report_name_start_with)

        # filename = "report_used_cars.csv"
        # path_save_report = "/tmp"

        if filename is None:
            print("No new report found")
            return {
                "statusCode": 200,
                "body": json.dumps({"message": "No new report found"})
            }
        
        file_path = f"{path_save_report}/{filename}"
        latest_file_path = f"/tmp/{filename}"

        # download_file_from_s3("s3://snd-hen-bucket/used_cars_reports/Book1.csv", latest_file_path)


        print(f" ****** DOWNLOADED REPORT: {filename} ****** ")

        if stage == 'post-inventory':
            process_post_inventory_report(file_path=file_path, latest_file_path=latest_file_path)
        elif stage == 'pre-inventory':
            pass
        elif stage == 'used-cars':
            process_used_cars_report(file_path=file_path, latest_file_path=latest_file_path)

    except Exception:
        print(traceback.format_exc())
        return {
            "statusCode": 500,
            "body": json.dumps({"message": "Report couldnt be processed correctly"})
        }

    return {
        "statusCode": 200,
        "body": json.dumps({"message": "Report processed correctly"})
    }


    

    
    
    
