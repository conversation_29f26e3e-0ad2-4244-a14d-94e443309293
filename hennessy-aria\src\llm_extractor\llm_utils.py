from openai import AzureOpenAI
from openai import OpenAI
import boto3
import json
from botocore.exceptions import ClientError


class LLM():
    def __init__(self, engine, llm_params):
        if 'gpt' in engine:
            if llm_params['deployment'] == 'azure':
                self.client = AzureOpenAI(
                    azure_endpoint = llm_params['endpoint'], 
                    api_key = llm_params['api_key'],  
                    api_version = llm_params['api_version']
                )
            elif llm_params['deployment'] == 'public':
                self.client = OpenAI(api_key=llm_params['api_key'])
            else:
                return 'Deployment not supported'
        elif llm_params['deployment'] == 'bedrock':
            self.client = boto3.client("bedrock-runtime", region_name=llm_params['region_name'])
        
        self.model = llm_params['model']
        self.engine = llm_params['engine']
        self.deployment = llm_params['deployment']
        self.prompt = ''

    def generate_prompt(self, prompt):
        self.prompt = prompt

    def send_message_to_llm(self, message):
        if 'gpt' in self.engine:
            try:
                print('------------Message to GPT-----------')
                print(message)
                print('-----------------------')
                response = self.client.chat.completions.create(
                    model = self.model,
                    messages=[
                        {"role": "system", "content": self.prompt},
                        {"role": "user", "content": message}
                    ],
                    response_format= { "type":"json_object" },
                    timeout = 120
                )
                print('------------GPT output-----------')
                print(response)
                print('-----------------------')
                return response.choices[0].message.content
            except Exception as e:
                error_message = e.body.get('message','')
                return json.dumps({'Error': error_message})
                
        elif self.deployment == 'bedrock':
            print(f'------------Message to {self.engine}-----------')
            print(message)
            print('-----------------------')
            response = self.client.converse(
                modelId=self.model,
                messages= [
                    {"role": "user", "content": [{"text": message}]}
                ],
                system=[
                    {'text': self.prompt},
                ],
                inferenceConfig={"maxTokens": 2000, "temperature": 0.7, "topP": 1},
            )
            print(f'------------{self.engine} output-----------')
            print(response)
            print('-----------------------')
            return response["output"]["message"]["content"][0]["text"]
        else:
            return 'Engine not supported'
