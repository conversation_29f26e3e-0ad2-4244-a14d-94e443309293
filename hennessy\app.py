import shutil
import json
import sys
import os
import threading
import string
import random
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append('/var/task')
        
from Invoices.config    import DefaultConfig
from Invoices.vendors   import FORD, LEXUS, JLR, PORSCHE, CADILLAC, HONDA, MANHEIM, MAZDA, GENERALMOTORS

import traceback

def generate_strong_password(min_length=8, max_length=15):
    if min_length < 8 or max_length > 15:
        raise ValueError("Password length must be between 8 and 15 characters.")

    # Selección de al menos un carácter por tipo requerido
    upper = random.choice(string.ascii_uppercase)
    lower = random.choice(string.ascii_lowercase)
    digit = random.choice(string.digits)
    special = random.choice(string.punctuation)

    # Caracteres obligatorios
    required_chars = [upper, lower, digit, special]

    # Cuántos caracteres aleatorios se necesitan para completar la longitud
    remaining_length = random.randint(min_length, max_length) - len(required_chars)

    # Pool completo para el resto de la contraseña
    all_chars = string.ascii_letters + string.digits + string.punctuation

    # Agregar caracteres aleatorios
    required_chars += random.choices(all_chars, k=remaining_length)

    # Mezclar el resultado
    random.shuffle(required_chars)

    return ''.join(required_chars)


def get_parameters():
    """to be replaced by secret manager"""
    return DefaultConfig.get_parameters()

def lambda_handler(event, context):
    action = event.get("action", "invoice_download")
    store = event['store']
    stage = event.get('stage', "")
    data = event.get('data', {})
    brand = data.get("brand", "")
    vins = data.get("vins", [])

    store = store.split("_")[0]

    print("Current directory: ", os.getcwd())
    os.chdir('/tmp')
    print("Modified directory: ", os.getcwd())


    try:
        if store == 'FOR':
            store_site = FORD(store, stage)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'LOA':
            store_site = LEXUS(store, stage)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'LOG':
            store_site = LEXUS(store, stage)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store in ['JLRN', 'JLRB', 'JLRG']:
            store_site = JLR(store, stage, brand)
            store_site.login(*DefaultConfig.get_user_password('JLRN'))
        elif store == 'POR':
            store_site = PORSCHE(store, stage)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'PNW':
            store_site = PORSCHE(store, stage)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'CAD':
            store_site = CADILLAC(store, stage)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'HON':
            store_site = HONDA(store, stage)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'MANHEIM':
            store_site = MANHEIM(store, stage)
            store_site.login(*DefaultConfig.get_user_password(store))
        elif store == 'MBG':
            if "MAZDA" in brand:
                store_site = MAZDA(store, stage)
                store_site.login(*DefaultConfig.get_user_password(store, brand))
            else:
                # store_site = GENERALMOTORS(store, stage)
                # store_site.login(*DefaultConfig.get_user_password(store, brand))
                raise Exception("Not Avaliable")
        else:
            return {
                "statusCode": 200,
                "body": json.dumps({})
            }
    
    except Exception as e:
        print("Initial login failed")
        print(f"Error: {traceback.format_exc()}")
        body = {}
        if len(vins) > 0:
            for i in vins:
                body[str(i)] = {
                    'error': str(e),
                    'stored': False,
                    'stage': None
                }
        else:
            body = {'error': e}
        return {
            "statusCode": 500,
            "body": json.dumps(body)
        }
    
    else:

        if action == "invoice_download":
            try:

                print("MIRAMIRA LOS VINS", vins)
                invoices_res = store_site.download_vins(vins)
                print(invoices_res)
            except Exception as e:
                print("Download invoices failed due to an unhandled exception")
                print(f"Error: {traceback.format_exc()}")

                tracker = store_site.tracker
                for v in vins:
                    if v not in tracker:
                        tracker[v] = {'error': None, 'stored': False, 'stage': None}
                return {
                    "statusCode": 500,
                    "body": json.dumps(tracker)
                }
                
            response = {
                "statusCode": 200,
                "body": json.dumps(invoices_res)
            }
            
        elif action == "reset_password":
            
            try: 
                
                user, password = DefaultConfig.get_user_password(store)
                secret_name = DefaultConfig.env + f"-user_login_{store.lower()}"

                if store in ['LOA', 'LOG']:
                    new_password = generate_strong_password()
                    changed = store_site.reset_password_and_refresh_secret(secret_name, user, password, new_password)
            
                    if changed == True:
                        return {
                            "statusCode": 200,
                            "body": json.dumps({'message': f'Password reseted successfully for store {store}'})
                        }

                return {
                    "statusCode": 200,
                    "body": json.dumps({'message': f'Password dont reseted for store {store}'})
                }
            
            except Exception as e:
                print("Refreshing password failed due to an unhandled exception")
                print(f"Error: {e}")
                return {
                    "statusCode": 500,
                    "body": json.dumps({'error': str(e)})
                }
            
        elif action == "download_honda_pricing_guide":
            if store == 'HON' or store == "ACU":
                try:
                    store_site.get_pricing_sheet()

                    return {
                        "statusCode": 200,
                        "body": json.dumps({'message': f'Successfully downloaded price sheet'})
                    }
            
                except Exception as e:
                    print("Couldnt download the pricing sheet")
                    print(f"Error: {e}")
                    return {
                        "statusCode": 500,
                        "body": json.dumps({'error': str(e)})
                    }
                
        elif action == "download_new_vehicles_report":
            if store == 'POR' or store == "PNW":
                try:
                    store_site.download_new_vehicles_report(store)

                    return {
                        "statusCode": 200,
                        "body": json.dumps({'message': f'Successfully downloaded price new vehicles report'})
                    }
            
                except Exception as e:
                    print("Couldnt download the new vehicles report")
                    print(f"Error: {e}")
                    return {
                        "statusCode": 500,
                        "body": json.dumps({'error': str(e)})
                    }
            
    return response


def call_log():
    for _ in range(1):
        lambda_handler({
            "store": "HON",
            "action": "download_honda_pricing_guide"

        }, None)

def call_loa():
    for _ in range(1):
        lambda_handler({
  "stage": "post-inventory",
  "data": {
    "brand": "GMC",
    "vins": [
      "1GT1ESEH6TU401744"
    ]
  },
  "store": "MBG"
}, None)

if __name__ == "__main__":
    t1 = threading.Thread(target=call_loa)
    #t2 = threading.Thread(target=call_loa)

    # Start threads
    t1.start()
    #t2.start()

    # Wait for both to finish
    t1.join()
    #t2.join()