# Define environment variables
export IMAGE_NAME=pyautomationaws/selenium
export AWS_REGION=us-east-1
export AWS_ACCOUNT_ID=************
export ECR_REPO=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$IMAGE_NAME
export LAMBDA_NAME=snd-hen-selenium-downloader

# Clean up any leftover apt files
rm -rf /var/lib/apt/lists/* /var/cache/apt/*

echo "Building Docker image..." &&
docker buildx build --platform linux/amd64 -t $IMAGE_NAME .
docker tag $IMAGE_NAME $ECR_REPO:latest &&

# Run the container and test the app
echo "Running the container..." &&
docker run --rm -it --entrypoint /bin/sh $IMAGE_NAME -c "
    mkdir -p /var/task && 
    mkdir -p /tmp && 
    rm -rf /tmp/* &&
    which python3 &&
    echo '----------------------------------------------------------------------------------------' &&
    echo 'Running the app...' &&
    python app.py && 
    exit
"