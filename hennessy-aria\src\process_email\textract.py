import os
from ocr import Ocr, OCRStatus
import time

"""
This class contains all the related to by giving the files it 
returs a string with all the text in it given by Textract
"""

class TextractResponseHandler():

    def __init__(self):
        

        self.TIMESTEPS_THRESHOLD = 20

        textract_adapter_details = {
            "region_name": "us-east-1",
            "bucket_name": os.getenv('BUCKET')
        }

        self.ocr_class = Ocr(textract_adapter_details)
    
    def send_file_to_textract(self, file_name, file_path):

        job_id, ocr_status = self.ocr_class.async_call_textract(file_path, file_name)

        return job_id, ocr_status
    
    def get_rest_of_file_if_needed(self, textract_response, job_id, next_token):
        
        next_token_responses = []
        next_token_responses.append(textract_response)

        while(next_token != ""):
            _, textract_next_token_response, next_token = self.ocr_class.get_response(job_id, next_token)
            next_token_responses.append(textract_next_token_response)
            
        return next_token_responses

    def get_text_from_file(self, file_name, file_path):
        """
        This methods handle all the logic to get the response we want. 
        First of all we have to send the document to textract in order
        to get the values.

        Later on we have to check if the job has done. This process could take
        some time that is why we have a while checking for the ocr status until
        the job is completed

        After that we have all the information that textract gave us so we have to
        parse it in order to get the complete "string" of all the word blocks

        """
        
        job_id, ocr_status = self.send_file_to_textract(file_name, file_path) 

        time.sleep(5)

        timesteps = 0
        # Async wait
        while(ocr_status != 1 and timesteps < self.TIMESTEPS_THRESHOLD):
            ocr_status, textract_response, next_token = self.ocr_class.get_response(job_id)
            time.sleep(1 * timesteps)

            full_response = self.get_rest_of_file_if_needed(textract_response, job_id, next_token) 

            timesteps += 1
            
            # If the file should be retry we send it again
            if ocr_status == OCRStatus.retry.value:
                job_id, ocr_status = self.send_file_to_textract(file_name)
                time.sleep(1 * timesteps)

        complete_text = ""
        if ocr_status == 1:
            word_blocks, complete_text = self.ocr_class.get_full_page_string(full_response)

        
        return complete_text


        