import json
import os
import traceback
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class ConfidenceLevel(Enum):
    HIGH = 0.9
    MEDIUM = 0.7
    LOW = 0.5


@dataclass
class OCRWord:
    text: str
    id: str
    confidence: float
    page: int
    coordinates: Optional[Dict[str, float]] = None


@dataclass
class ProcessingResult:
    success: bool
    data: Dict[str, Any]
    error: Optional[str] = None
    pages_processed: int = 0


class DocumentProcessor:
    """
    Document processing class that handles large JSON data with chunking,
    multiple data formats, and error handling strategies.
    """
    
    def __init__(self, max_pages_per_chunk: int = 10, confidence_threshold: float = 0.7):
        self.max_pages_per_chunk = max_pages_per_chunk
        self.confidence_threshold = confidence_threshold
        self.extraction_templates = self._load_extraction_templates()
        
    def _load_extraction_templates(self) -> Dict[str, Dict]:
        """Load JSON templates for different document types"""
        return {
            "invoice": {
                "VIN": "17-character Vehicle Identification Number",
                "Total_Dealer_Invoice": "Total dealer invoice amount",
                "Model_Code": "Alphanumeric car model code",
                "Model_Description": "Car model description",
                "Invoice_Date": "Date when invoice was issued",
                "Delivery_Charge": "Delivery processing fee amount"
            },
            "title": {
                "VIN": "17-character Vehicle Identification Number",
                "Make": "Vehicle manufacturer",
                "Model": "Vehicle model",
                "Year": "Vehicle year",
                "Owner_Name": "Registered owner name",
                "Issue_Date": "Title issue date"
            },
            "bol": {
                "VIN_List": "List of Vehicle Identification Numbers",
                "Origin": "Origin location",
                "Destination": "Destination location",
                "Carrier": "Transportation carrier",
                "Date": "Bill of lading date"
            }
        }
    
    def load_dummy_ocr_data(self) -> List[OCRWord]:
        """Load dummy OCR data from static JSON file"""
        try:
            with open('/tmp/dummy_ocr_data.json', 'r') as f:
                dummy_data = json.load(f)
        except FileNotFoundError:
            # Fallback to inline dummy data if file doesn't exist
            dummy_data = self._generate_dummy_data()
            
        ocr_words = []
        for item in dummy_data:
            ocr_words.append(OCRWord(
                text=item['text'],
                id=item['id'],
                confidence=item['confidence'],
                page=item['page'],
                coordinates=item.get('coordinates')
            ))
        return ocr_words
    
    def _generate_dummy_data(self) -> List[Dict]:
        """Generate dummy OCR data for testing"""
        dummy_data = []
        pages = 25  # Simulate a 25-page document
        word_id = 1
        
        for page in range(1, pages + 1):
            # Add some invoice-like content for each page
            page_words = [
                {"text": "INVOICE", "confidence": 0.95},
                {"text": "VIN:", "confidence": 0.92},
                {"text": f"1HGBH41JXMN10{page:04d}", "confidence": 0.88},
                {"text": "Model:", "confidence": 0.91},
                {"text": "Honda", "confidence": 0.89},
                {"text": "Civic", "confidence": 0.87},
                {"text": "Total:", "confidence": 0.93},
                {"text": f"${25000 + page * 100}.00", "confidence": 0.90},
                {"text": "Date:", "confidence": 0.94},
                {"text": f"01/{page:02d}/2024", "confidence": 0.86},
                {"text": "Delivery", "confidence": 0.85},
                {"text": "Charge:", "confidence": 0.83},
                {"text": f"${500 + page * 10}.00", "confidence": 0.81}
            ]
            
            for word_data in page_words:
                dummy_data.append({
                    "text": word_data["text"],
                    "id": str(word_id),
                    "confidence": word_data["confidence"],
                    "page": page,
                    "coordinates": {
                        "x": 100 + (word_id % 10) * 50,
                        "y": 100 + (word_id % 5) * 30,
                        "width": len(word_data["text"]) * 8,
                        "height": 20
                    }
                })
                word_id += 1
                
        return dummy_data
    
    def chunk_pages(self, ocr_words: List[OCRWord]) -> List[List[OCRWord]]:
        """
        Chunk document pages into batches of max_pages_per_chunk
        """
        if not ocr_words:
            return []
            
        # Group words by page
        pages_dict = {}
        for word in ocr_words:
            if word.page not in pages_dict:
                pages_dict[word.page] = []
            pages_dict[word.page].append(word)
        
        # Sort pages and create chunks
        sorted_pages = sorted(pages_dict.keys())
        chunks = []
        
        for i in range(0, len(sorted_pages), self.max_pages_per_chunk):
            chunk_pages = sorted_pages[i:i + self.max_pages_per_chunk]
            chunk_words = []
            for page_num in chunk_pages:
                chunk_words.extend(pages_dict[page_num])
            chunks.append(chunk_words)
            
        return chunks
    
    def apply_confidence_filtering(self, ocr_words: List[OCRWord]) -> List[OCRWord]:
        """
        Filter OCR words based on confidence threshold to reduce data size
        """
        return [word for word in ocr_words if word.confidence >= self.confidence_threshold]
    
    def format_raw_text_ocr(self, ocr_words: List[OCRWord]) -> List[Dict]:
        """
        Format OCR data with IDs for precise extraction
        """
        return [
            {
                "Text": word.text,
                "Id": word.id,
                "Page": word.page,
                "Confidence": word.confidence
            }
            for word in ocr_words
        ]
    
    def format_raw_text_plain(self, ocr_words: List[OCRWord]) -> str:
        """
        Format OCR data as plain text without IDs for smaller payload
        """
        # Group by page and join text
        pages_text = {}
        for word in ocr_words:
            if word.page not in pages_text:
                pages_text[word.page] = []
            pages_text[word.page].append(word.text)
        
        # Join all pages
        all_text = []
        for page_num in sorted(pages_text.keys()):
            page_text = " ".join(pages_text[page_num])
            all_text.append(f"Page {page_num}: {page_text}")
        
        return "\n\n".join(all_text)
    
    def format_raw_text_with_coords(self, ocr_words: List[OCRWord]) -> List[Dict]:
        """
        Format OCR data with coordinate information for spatial analysis
        """
        return [
            {
                "Text": word.text,
                "Id": word.id,
                "Page": word.page,
                "Confidence": word.confidence,
                "Coords": word.coordinates
            }
            for word in ocr_words if word.coordinates
        ]


def lambda_handler(event, context):
    """
    AWS Lambda handler for document processing with chunking and multiple data formats
    """
    try:
        # Initialize processor
        processor = DocumentProcessor(
            max_pages_per_chunk=event.get('max_pages_per_chunk', 10),
            confidence_threshold=event.get('confidence_threshold', 0.7)
        )
        
        # Extract parameters from event
        document_type = event.get('document_type', 'invoice')
        processing_mode = event.get('processing_mode', 'all')  # 'all', 'chunked', 'filtered'
        page_range = event.get('page_range')  # Optional: [start_page, end_page]
        
        print(f"Processing document type: {document_type}")
        print(f"Processing mode: {processing_mode}")
        print(f"Max pages per chunk: {processor.max_pages_per_chunk}")
        print(f"Confidence threshold: {processor.confidence_threshold}")
        
        # Load OCR data
        ocr_words = processor.load_dummy_ocr_data()
        print(f"Loaded {len(ocr_words)} OCR words from {max([w.page for w in ocr_words])} pages")
        
        # Apply page range filter if specified
        if page_range and len(page_range) == 2:
            start_page, end_page = page_range
            ocr_words = [w for w in ocr_words if start_page <= w.page <= end_page]
            print(f"Filtered to pages {start_page}-{end_page}: {len(ocr_words)} words")
        
        # Apply confidence filtering
        if processing_mode in ['filtered', 'all']:
            filtered_words = processor.apply_confidence_filtering(ocr_words)
            print(f"Confidence filtering: {len(ocr_words)} -> {len(filtered_words)} words")
        else:
            filtered_words = ocr_words
        
        # Chunk the document
        chunks = processor.chunk_pages(filtered_words)
        print(f"Created {len(chunks)} chunks")
        
        # Process each chunk
        results = []
        total_pages_processed = 0
        
        for chunk_idx, chunk_words in enumerate(chunks):
            try:
                print(f"Processing chunk {chunk_idx + 1}/{len(chunks)} with {len(chunk_words)} words")
                
                # Get pages in this chunk
                chunk_pages = sorted(list(set([w.page for w in chunk_words])))
                
                # Format data in multiple formats
                chunk_result = {
                    "chunk_id": chunk_idx + 1,
                    "pages": chunk_pages,
                    "word_count": len(chunk_words),
                    "formats": {}
                }
                
                # Format 1: Raw text with IDs (for precise extraction)
                if processing_mode in ['all', 'chunked']:
                    chunk_result["formats"]["raw_text_ocr"] = processor.format_raw_text_ocr(chunk_words)
                
                # Format 2: Plain text (for smaller payload)
                chunk_result["formats"]["raw_text_plain"] = processor.format_raw_text_plain(chunk_words)
                
                # Format 3: Text with coordinates (for spatial analysis)
                if processing_mode in ['all', 'chunked']:
                    chunk_result["formats"]["raw_text_with_coords"] = processor.format_raw_text_with_coords(chunk_words)
                
                # Add extraction template for this document type
                if document_type in processor.extraction_templates:
                    chunk_result["extraction_template"] = processor.extraction_templates[document_type]
                
                # Simulate LLM processing with error handling
                try:
                    # Check if chunk size might exceed context limits
                    estimated_tokens = len(json.dumps(chunk_result["formats"]["raw_text_ocr"])) // 4
                    if estimated_tokens > 100000:  # Simulate context limit
                        raise Exception("maximum context length exceeded")
                    
                    # Simulate successful processing
                    chunk_result["llm_processing"] = {
                        "status": "success",
                        "estimated_tokens": estimated_tokens,
                        "extracted_fields": {
                            field: f"extracted_{field}_value_chunk_{chunk_idx + 1}"
                            for field in processor.extraction_templates.get(document_type, {}).keys()
                        }
                    }
                    
                except Exception as llm_error:
                    # Graceful fallback when context limits are hit
                    if "maximum context length" in str(llm_error):
                        print(f"Context limit exceeded for chunk {chunk_idx + 1}, using fallback")
                        chunk_result["llm_processing"] = {
                            "status": "fallback",
                            "error": "context_limit_exceeded",
                            "fallback_result": "processed_with_reduced_data"
                        }
                    else:
                        chunk_result["llm_processing"] = {
                            "status": "error",
                            "error": str(llm_error)
                        }
                
                results.append(chunk_result)
                total_pages_processed += len(chunk_pages)
                
            except Exception as chunk_error:
                print(f"Error processing chunk {chunk_idx + 1}: {str(chunk_error)}")
                results.append({
                    "chunk_id": chunk_idx + 1,
                    "error": str(chunk_error),
                    "status": "failed"
                })
        
        # Compile final response
        response = {
            "statusCode": 200,
            "body": {
                "processing_summary": {
                    "document_type": document_type,
                    "processing_mode": processing_mode,
                    "total_words": len(ocr_words),
                    "filtered_words": len(filtered_words),
                    "total_chunks": len(chunks),
                    "pages_processed": total_pages_processed,
                    "max_pages_per_chunk": processor.max_pages_per_chunk,
                    "confidence_threshold": processor.confidence_threshold
                },
                "chunks": results,
                "extraction_template": processor.extraction_templates.get(document_type, {}),
                "processing_strategies": {
                    "document_chunking": f"Max {processor.max_pages_per_chunk} pages per LLM call",
                    "multiple_data_formats": [
                        "raw_text_ocr (with IDs) - for precise extraction",
                        "raw_text_plain (without IDs) - for smaller payload", 
                        "raw_text_with_coords (with coordinates) - for spatial analysis"
                    ],
                    "error_handling": "Graceful fallback when context limits are hit",
                    "json_template_embedding": "Schema is part of prompt, not message data",
                    "confidence_filtering": f"Remove OCR text below {processor.confidence_threshold} confidence",
                    "batch_processing": "Process document chunks sequentially"
                }
            }
        }
        
        print(f"Successfully processed {len(chunks)} chunks with {total_pages_processed} pages")
        return response
        
    except Exception as e:
        print(f"Lambda execution error: {str(e)}")
        print(traceback.format_exc())
        
        return {
            "statusCode": 500,
            "body": {
                "error": str(e),
                "message": "Document processing failed",
                "traceback": traceback.format_exc()
            }
        }


# Test function for local development
if __name__ == "__main__":
    # Test event
    test_event = {
        "document_type": "invoice",
        "processing_mode": "all",
        "max_pages_per_chunk": 10,
        "confidence_threshold": 0.7,
        "page_range": [1, 15]  # Process only first 15 pages
    }
    
    result = lambda_handler(test_event, None)
    print(json.dumps(result, indent=2))
