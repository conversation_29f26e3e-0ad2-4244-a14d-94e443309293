from Utilities.Common_Utilities.logger_utility import Logger
import base64
import traceback

class Base64():

    def __init__(self, log_path):        
        self.l = Logger(log_path)

    # Conver image to base65
    #   INPUT:
    #       -image_path
    #   OUTPUT
    #       -encoded_string: image converted into base64
    def image_to_base64(self, image_path):
        try:
            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read())
                return encoded_string
        except Exception as e:
            raise Exception ('Exception occurred on image_to_base64 method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))