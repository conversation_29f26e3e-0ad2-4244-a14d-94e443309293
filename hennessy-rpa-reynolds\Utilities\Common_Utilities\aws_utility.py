from Utilities.Common_Utilities.logger_utility import Logger
from Utilities.Common_Utilities.http_utility import Http
import traceback
import boto3

class Aws():

    def __init__(self, log_path, access_key = None, secret_access_key = None, region_name = None):        
        self.l = Logger(log_path)
        self.http = Http(log_path)
        self.access_key = access_key
        self.secret_access_key = secret_access_key
        self.region_name = region_name

    # Upload a file into a aws bucket
    #   INPUT:
    #       -local_file: path of the local file
    #       -bucket: name of aws bucket
    #       -s3_file: path to save the file on the aws bucket
    def upload_to_aws(self, local_file, bucket, s3_file):
        try:
            s3 = boto3.client('s3', aws_access_key_id=self.access_key, aws_secret_access_key=self.secret_access_key, region_name=self.region_name)
            s3.upload_file(local_file, bucket, s3_file)
        except Exception as e:
            raise Exception ('Exception occurred on upload_to_aws method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Download a file from aws bucket
    #   INPUT:
    #       -local_file: path where local file will be downloaded
    #       -bucket: name of aws bucket
    #       -s3_file: path of the aws file that will be downloaded
    def download_from_aws(self, s3_file, bucket, local_file):
        try:
            s3 = boto3.client('s3', aws_access_key_id=self.access_key, aws_secret_access_key=self.secret_access_key, region_name=self.region_name)
            s3.download_file(bucket, s3_file, local_file)
        except Exception as e:           
            raise Exception ('Exception occurred on download_to_aws method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))
        
    # Secrets handler
    #   INPUT:
    #       -url: url of aws's endpoint where secrets are stores
    #       -method: get, create, delete or update
    #       -cred_name: credential name
    #       -cred_value: value to update/create (only applies for create and update)   
    #       -token
    #   OUTPUT:
    #       -credential_value (only applies to get)
    def secrets_handler(self, url, method, cred_name, cred_value, token):
        try: 
            if method in ['get', 'create', 'delete', 'update']: 
                body = {
                    "action": method,
                    "secret_data":{
                        cred_name: cred_value
                    }
                }
                response = self.http.http_request('POST', url, body, token)
                if response.text == '':
                    raise Exception ('Exception occurred on secrets_handler method. Details: Credential {} not valid'.format(cred_name))
                return response.text
            else:
                raise Exception ('Exception occurred on secrets_handler method. Details: Method not valid')
        except Exception as e:           
            raise Exception ('Exception occurred on download_to_aws method. Details: ' + str(e) + '. More info: ' + str(traceback.format_exc()))