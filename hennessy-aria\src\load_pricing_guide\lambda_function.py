from boto3_utils import download_file_from_s3, trigger_lambda_response, get_secret
import pymongo
import os
import json
import traceback
from datetime import datetime, timedelta
import fitz

def get_s3_url(folder, date, store):
    if "HON" in store:
        return f"s3://{os.environ['BUCKET']}/{folder}/{date.year}/{date.strftime('%m')}/{date.strftime('%d')}/Honda_HON_pricing_sheet.pdf"

    if "ACU" in store:
        return f"s3://{os.environ['BUCKET']}/{folder}/{date.year}/{date.strftime('%m')}/{date.strftime('%d')}/Acura_ACU_pricing_sheet.pdf"

def download_pricing_guide(date, file_path, store):
    folder = os.environ['PRICING_GUIDE_FOLDER']
    s3_url = get_s3_url(folder, date, store)
    return download_file_from_s3(s3_url, file_path), s3_url

def extract_content_text(doc):
    content = []
    for page in doc:
        text = page.get_text("text")  # Get text in reading order
        content.append(text.strip())
    return content


def compare_pdf_content(file1, file2):
    doc1 = fitz.open(file1)
    doc2 = fitz.open(file2)

    if len(doc1) != len(doc2):
        print(f"Different page counts: {len(doc1)} vs {len(doc2)}")
        return False

    content1 = extract_content_text(doc1)
    content2 = extract_content_text(doc2)

    for i, (p1, p2) in enumerate(zip(content1, content2), start=1):
        if p1 != p2:
            print(f"Difference found on page {i}")
            return False

    print("The pricing guide that we are trying to load its the same that its loaded!")
    return True

def lambda_handler(event, context):

    print(event)

    store = event.get("store", None)
    if store is None:
        return {"statusCode": 400, "body": json.dumps({"error": "Store didnt come in the event...Skipping..."})}

    today = datetime.today()

    file_path = "/tmp/last_pricing_guide.pdf"
    
    downloaded_today, todays_s3_url = download_pricing_guide(today, file_path, store)

    if not downloaded_today:
        return {"statusCode": 400, "body": json.dumps({"error": "Honda table couldn't be downloaded successfully."})}
    
    mongo_uri = get_secret(f"{os.environ['ENV']}-mongodb_uri", return_json = False)
    client = pymongo.MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
    db = client[os.environ['MONGO_DATABASE']]
    pricing_sheet_collection = db.get_collection(f"{store.lower()}_pricing_sheet")
    
    file_path_older = "/tmp/older_pricing_guide.pdf"
    info_last_loaded_file = pricing_sheet_collection.find_one({"pricing_guide": "info"})
    downloaded_older = None
    if info_last_loaded_file is not None:
        downloaded_older = download_file_from_s3(info_last_loaded_file["s3_url"], file_path_older)

    if info_last_loaded_file is not None and downloaded_older:
        if compare_pdf_content(file_path, file_path_older):
            return {"pages_to_process": []}

    rows_info = pricing_sheet_collection.find({})
    for row in rows_info:
        if row.get("pricing_guide", "") == "info":
            continue
        else:
            pricing_sheet_collection.update_one({"_id": row["_id"]}, {"$set": {"erasable": True}})

    pricing_sheet_collection.insert_one({"pricing_guide": "info", "created_at": datetime.now(), "s3_url": todays_s3_url})
    print(f"Collection {store.lower()}_pricing_sheet has been deleted.")

    try:
        pdf_processer_lambda_response = trigger_lambda_response(os.environ['PDF_PROCESSER_LAMBDA'], {"action": "process_document", "s3_file": todays_s3_url})
        if isinstance(pdf_processer_lambda_response, str):
            pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response)
        
        print(f" ****** RESPONSE FROM PDF_PROCESSOR ****** ")
        print(pdf_processer_lambda_response)
        print("********************************************")

        if pdf_processer_lambda_response['statusCode'] != 200:
            return {
                "statusCode": 500,
                "body": json.dumps({"message": "Error response from pdf_utils."})
            }
        
        pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response['body'])
        if isinstance(pdf_processer_lambda_response, str):
            pdf_processer_lambda_response = json.loads(pdf_processer_lambda_response)

        pricing_guide_pdfs = pdf_processer_lambda_response['splitted_pages']
        pricing_guide_images = pdf_processer_lambda_response['splitted_images']

        return {
            "pages_to_process": [{"splitted_page": pricing_guide_pdfs[i], "splitted_img": pricing_guide_images[i]} for i in range(len(pricing_guide_images))]
        }
                
    except Exception:
        print("Exception: ", traceback.format_exc())
        return {
            "statusCode": 200,
            "body": json.dumps({"error": "Honda table couldnt be loaded successfully."})
        }