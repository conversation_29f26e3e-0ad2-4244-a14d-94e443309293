ARG LAMBDA_TASK_ROOT="/var/task"

FROM mcr.microsoft.com/playwright/python:v1.47.0-jammy AS build-image

# Install aws-lambda-cpp build dependencies
RUN apt-get update && \
    apt-get install -y \
    g++ \
    make \
    cmake \
    unzip \
    libcurl4-openssl-dev

ARG LAMBDA_TASK_ROOT

# Create function directory
RUN mkdir -p ${LAMBDA_TASK_ROOT}

WORKDIR ${LAMBDA_TASK_ROOT}

# Install the runtime interface client
RUN pip install  \
    --target ${LAMBDA_TASK_ROOT} \
    awslambdaric

# Multi-stage build: grab a fresh copy of the base image
FROM mcr.microsoft.com/playwright/python:v1.47.0-jammy

ENV HOME="/tmp"

ARG LAMBDA_TASK_ROOT

# Set working directory to function root directory
WORKDIR ${LAMBDA_TASK_ROOT}

# Copy in the build image dependencies
COPY --from=build-image ${LAMBDA_TASK_ROOT} ${LAMBDA_TASK_ROOT}

# Install libmagic
RUN apt-get update && apt-get install -y libmagic-dev

# Install base requirements
COPY ./requirements.txt ${LAMBDA_TASK_ROOT}/requirement-base.txt
RUN pip install -r ${LAMBDA_TASK_ROOT}/requirement-base.txt


ARG WORKER_NAME

# Install worker-specific requirements
COPY ./workers/${WORKER_NAME}/requirements.txt* ${LAMBDA_TASK_ROOT}/
RUN if [ -f ${LAMBDA_TASK_ROOT}/requirements.txt ]; then \
        pip install -r ${LAMBDA_TASK_ROOT}/requirements.txt; \
    fi

COPY ./lib/sync_worker ${LAMBDA_TASK_ROOT}/sync_worker
COPY ./app.py ${LAMBDA_TASK_ROOT}/
COPY ./workers/${WORKER_NAME}/worker ${LAMBDA_TASK_ROOT}/worker

ENTRYPOINT [ "/usr/bin/python", "-m", "awslambdaric" ]
CMD [ "app.lambda_handler" ]
