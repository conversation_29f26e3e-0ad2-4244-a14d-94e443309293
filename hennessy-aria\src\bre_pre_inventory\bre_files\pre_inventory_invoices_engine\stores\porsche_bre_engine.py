from bre_files.pre_inventory_invoices_engine.invoice_bre_engine import PreInventoryInvoicesBreEngine
from vininfo import Vin
import os
import pymongo

class PorscheInvoicesBreEngine(PreInventoryInvoicesBreEngine):

    def __init__(self, event, mongo_uri):
        super().__init__(event, mongo_uri)
        
        self.rules = {
            "required_fields_and_calculations": [801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 824, 1],
            "submit": [801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 824, 1],
            "save": [],
            "review": [],
            "inspection": [],
            "manually": []
        }

        self.bre_rules.update({
            819: { # Fill General color in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [810],
            },
            820: { # Fill Exterior color in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [810],
            },
            821: { # Fill Interior color in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [810],
            },
            822: { # Fill List price in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [1],
            },
            824: { # Fill Base MSRP in DMS data
                'tag': 'stock_in_values',
                'output_tags': ['stock_in_values'],
                'pre_req': [1],
            },
            1: { # Retail total price non empty
                'tag': 'retail_total_price',
                'output_tags': ['retail_total_price'],
                'pre_req': [],
            }
        })

        self.valid_rules = self.rules[self.bre_type[self.document_type]]
        print("RULES TO EXECUTE", self.valid_rules)
        self.store = self.parsed_fields[self.document_type]['store'].get('value')

    def rule_1(self):
        input_data = str(self.parsed_fields[self.document_type]['retail_total_price'].get('value')) if self.parsed_fields[self.document_type].get('retail_total_price', {}).get("value", "") != "" else ''
        self.non_empty_rule(input_data, 'Retail total price')

    def rule_819(self):
        field_name = "GENERAL COLOR"
        
        color_code = str(self.parsed_fields[self.document_type]['color'].get('value')).replace(" ", "") if self.parsed_fields[self.document_type].get('color', {}).get("value", "") != "" else ''
        general_color = color_code[:2]

        general_color_value = ""
        rows_table = self.parsed_fields[self.document_type]['table'].get("rows", {}) or self.parsed_fields[self.document_type]['table'].get("value", {})
        for k, v in rows_table.items():
            field_name_code = v['cells']['code']['value']
            if field_name_code.replace(" ", "").lower() == general_color.replace(" ", "").lower():
                general_color_value = v['cells']['description']['value']
                break
            
        self.fill_dms_data_row(general_color_value, field_name)   

    def rule_820(self):
        field_name = "EXTERIOR COLOR"
        
        color_code = str(self.parsed_fields[self.document_type]['color'].get('value')).replace(" ", "") if self.parsed_fields[self.document_type].get('color', {}).get("value", "") != "" else ''
        general_color = color_code[2:4]

        exterior_color_value = ""
        rows_table = self.parsed_fields[self.document_type]['table'].get("rows", {}) or self.parsed_fields[self.document_type]['table'].get("value", {})
        for k, v in rows_table.items():
            field_name_code = v['cells']['code']['value']
            if field_name_code.replace(" ", "").lower() == general_color.replace(" ", "").lower():
                exterior_color_value = v['cells']['description']['value']
                break

        self.fill_dms_data_row(exterior_color_value, field_name)  

    def rule_821(self):
        field_name = "INTERIOR COLOR"
        
        color_code = str(self.parsed_fields[self.document_type]['color'].get('value')).replace(" ", "") if self.parsed_fields[self.document_type].get('color', {}).get("value", "") != "" else ''
        general_color = color_code[-2:]

        interior_color_value = ""
        rows_table = self.parsed_fields[self.document_type]['table'].get("rows", {}) or self.parsed_fields[self.document_type]['table'].get("value", {})
        for k, v in rows_table.items():
            field_name_code = v['cells']['code']['value']
            if field_name_code.replace(" ", "").lower() == general_color.replace(" ", "").lower():
                interior_color_value = v['cells']['description']['value']
                break

        self.fill_dms_data_row(interior_color_value, field_name)  

    def rule_822(self):
        field_name = "LIST PRICE"
        value = str(self.parsed_fields[self.document_type]['retail_total_price'].get('value')) if self.parsed_fields[self.document_type].get('retail_total_price', {}).get("value", "") != "" else ""
        self.fill_dms_data_row(value, field_name)
    
    def rule_824(self):
        field_name = "BASE MSRP"
        value = str(self.parsed_fields[self.document_type]['retail_total_price'].get('value')) if self.parsed_fields[self.document_type].get('retail_total_price', {}).get("value", "") != "" else ""
        self.fill_dms_data_row(value, field_name)