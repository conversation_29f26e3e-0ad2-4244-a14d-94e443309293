functions:

  lambda_authorizer:
    name: snd-hen-lambda_authorizer
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 128
    timeout: 180
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: LambdaAuthorizerRole
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/lambda_authorizer.zip
      patterns:
        - "!**/*"
        - src/lambda_authorizer/**
  
  bre_handler:
    name: snd-hen-bre_handler
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      BRE_LAMBDA: ${self:provider.stage}-bre
      <PERSON><PERSON>_LAMBDA_PRE_INVENTORY: ${self:provider.stage}-bre_pre_inventory
      BRE_LAMBDA_USED_CARS: ${self:provider.stage}-bre_used_cars
      LLM_LAMBDA: ${self:provider.stage}-llm_extractor
      LLM_LAMBDA_PRE_INVENTORY: ${self:provider.stage}-llm_extractor_pre_inventory
      LLM_LAMBDA_USED_CARS: ${self:provider.stage}-llm_extractor_used_cars
      ARIA_APP_ID_POST_INVENTORY: 73108b73-7960-4039-b5bf-bbc11cc606be
      ARIA_APP_ID_BOLS: 4ab7fb3c-2247-459e-85b5-cdb6dc9c19fe
      ARIA_APP_ID_TITLES: 09ccde48-4ed6-46e0-8ab4-174ca0484919
      ARIA_APP_ID_PRE_INVENTORY: 
      ARIA_APP_ID_USED_CARS: 9459a3e3-dcee-43ca-b46a-71857d605ba6
    role: BreHandlerRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
      # - {Ref: Boto3LayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0b48d49b8580b9bee
        - sg-0073e23cc9281b4f2
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/bre_handler.zip
      patterns:
        - "!**/*"
        - src/bre_handler/**
    events:
      - httpApi:
          path: /${self:provider.stage}/bre_handler
          method: post
          authorizer:
            name: lambda_authorizer
            type: request

  bre:
    name: snd-hen-bre
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 2048
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      DEFAULT_BRE_TYPE: required_fields_and_calculations
      LLM_MESSENGER_LAMBDA: ${self:provider.stage}-llm_messenger
      ARIA_ENV: sandbox
    role: BreRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: VinInfoLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/bre.zip
      patterns:
        - "!**/*"
        - src/bre/**

  bre_used_cars:
    name: snd-hen-bre_used_cars
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 2048
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      DEFAULT_BRE_TYPE: required_fields_and_calculations
      LLM_MESSENGER_LAMBDA: ${self:provider.stage}-llm_messenger
      ARIA_ENV: sandbox
    role: BreUsedCarsRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: VinInfoLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/bre_used_cars.zip
      patterns:
        - "!**/*"
        - src/bre_used_cars/**

  llm_messenger:
    name: snd-hen-llm_messenger
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      SECRET_LLM_CREDENTIALS: ${self:provider.stage}-llm_params      
    role: LlmMessengerRole
    layers:
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: OpenAILayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pydantic:13
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/llm_messenger.zip
      patterns:
        - "!**/*"
        - src/llm_messenger/**
  
  pdf_utils:
    name: snd-hen-pdf_utils
    handler: lambda_function.lambda_handler
    runtime: python3.12
    architecture: x86_64
    memorySize: 2048
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
    role: PdfUtilsRole
    layers: 
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p312-PyMuPDF:3
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-reportlab:6
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/pdf_utils.zip
      patterns:
        - "!**/*"
        - src/pdf_utils/**
        
  email_watcher:
    name: snd-hen-email_watcher
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      DUPLICATED_FOLDER: Duplicates
      ENV: ${self:provider.stage}
      FILTER_VALUE: ''
      MONGO_DATABASE: snd_hennessy
      MOVE_EMAILS: false
      MOVE_EMAIL_LAMBDA: ${self:provider.stage}-move_email
      PROCESSED_FOLDER: Processing
      PROCESS_EMAIL_LAMBDA: ${self:provider.stage}-process_email
      REPORT_EMAIL_USERS: <EMAIL>,<EMAIL>
      SOURCE_FOLDER: inbox
      TOP_FETCH: 50
      UNSORTED_FOLDER: 04-Unsorted
    role: EmailWatcherRole
    layers:
      - {Ref: MsalLayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/email_watcher.zip
      patterns:
        - "!**/*"
        - src/email_watcher/**

  move_email:
    name: snd-hen-move_email
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MOVE_EMAILS: false
    role: MoveEmailRole
    layers:
      - {Ref: MsalLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/move_email.zip
      patterns:
        - "!**/*"
        - src/move_email/**

  process_email:
    name: snd-hen-process_email
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ARIA_APP_ID: 4ab7fb3c-2247-459e-85b5-cdb6dc9c19fe
      ARIA_ENV: sandbox
      ENV: ${self:provider.stage}
      BUCKET: ${self:provider.stage}-bucket
      FILTER_VALUE: ''
      GROUP_NAME: bol
      MONGO_DATABASE: snd_hennessy
      MOVE_EMAILS: false
      MOVE_EMAIL_LAMBDA: ${self:provider.stage}-move_email
      PROCESSED_FOLDER: Processing
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
      LLM_MESSENGER_LAMBDA: ${self:provider.stage}-llm_messenger
      PROCESS_EMAIL_LAMBDA: ${self:provider.stage}-process_email
      SOURCE_FOLDER: act_test_copy_dont_delete
      UNSORTED_FOLDER: 04-Unsorted
      ATTACHMENTS_FOLDER: attachments_files
    role: ProcessEmailRole
    layers:
      - {Ref: MsalLayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyPDF2LayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/process_email.zip
      patterns:
        - "!**/*"
        - src/process_email/**

  llm_extractor:
    name: snd-hen-llm_extractor
    handler: lambda_function.lambda_handler
    runtime: python3.10
    architecture: x86_64
    memorySize: 2000
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BRE_LAMBDA: ${self:provider.stage}-bre
      ENV: ${self:provider.stage}
      LLM_EXTRACTOR_COLLECTION_NAME: llm_extractor
      MONGO_DATABASE: snd_hennessy
      aria_env: sandbox
      RETRYS: 3
    role: LLMExtractorRole
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: OpenAILayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}      
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/llm_extractor.zip
      patterns:
        - "!**/*"
        - src/llm_extractor/**

  llm_extractor_used_cars:
    name: snd-hen-llm_extractor_used_cars
    handler: lambda_function.lambda_handler
    runtime: python3.10
    architecture: x86_64
    memorySize: 2000
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BRE_LAMBDA_USED_CARS: ${self:provider.stage}-bre_used_cars
      ENV: ${self:provider.stage}
      LLM_EXTRACTOR_COLLECTION_NAME: llm_extractor
      MONGO_DATABASE: snd_hennessy
      aria_env: sandbox
      RETRYS: 3
    role: LLMExtractorUsedCarsRole
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: OpenAILayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}      
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/llm_extractor_used_cars.zip
      patterns:
        - "!**/*"
        - src/llm_extractor_used_cars/**

  report_sftp_to_s3:
    name: snd-hen-report_sftp_to_s3
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      REYNOLS_REPORT_FOLDER: reynols_reports
      SFTP_CREDENTIALS: ${self:provider.stage}-sftp_credentials
      SFTP_FILES_PATH: /invokescans/reports
      FILE_SFTP_EXTENSION: .csv
      PRE_INVENTORY_REPORT_FOLDER:
      SFTP_FILES_PATH_PRE_INVENTORY:
      USED_CARS_REPORT_FOLDER: reynols_reports
      SFTP_FILES_PATH_USED_CARS:
    role: ReportSFTPToS3Role
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-paramiko:13
      - {Ref: VinInfoLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/reynols_report_processor.zip
      patterns:
        - "!**/*"
        - src/reynols_report_processor/**

  invoice_downloader:
    name: snd-hen-invoice_downloader
    handler: lambda_function.lambda_handler
    runtime: python3.12
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      INVOICES_FOLDER: invoices
      INVOICES_FOLDER_PRE_INVENTORY: 
      INVOICES_FOLDER_USED_CARS: invoices_used_cars
    role: InvoiceDownloaderRole
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/invoice_downloader.zip
      patterns:
        - "!**/*"
        - src/invoice_downloader/**

  invoice_to_aria:
    name: snd-hen-invoice_to_aria
    handler: lambda_function.lambda_handler
    runtime: python3.12
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ARIA_ENV: sandbox
      GROUP_NAME_POST_INVENTORY: invoice
      ARIA_APP_ID_POST_INVENTORY: 73108b73-7960-4039-b5bf-bbc11cc606be
      GROUP_NAME_PRE_INVENTORY: 
      ARIA_APP_ID_PRE_INVENTORY: 
      GROUP_NAME_USED_CARS: used_cars_invoices
      ARIA_APP_ID_USED_CARS: 9459a3e3-dcee-43ca-b46a-71857d605ba6
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
    role: InvoiceToAriaRole
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/invoice_to_aria.zip
      patterns:
        - "!**/*"
        - src/invoice_to_aria/**

  title_sftp_to_s3_to_aria:
    name: snd-hen-title_sftp_to_s3_to_aria
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ARIA_ENV: sandbox
      GROUP_NAME: title
      ARIA_APP_ID: 09ccde48-4ed6-46e0-8ab4-174ca0484919
      COMPLETE_TITLE_FOLDER: titles
      ENV: ${self:provider.stage}
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
      LLM_MESSENGER_LAMBDA: ${self:provider.stage}-llm_messenger
      SFTP_FILES_PATH: /invokescans/Titles_test
      FILE_SFTP_EXTENSION: .pdf
      MONGO_DATABASE: snd_hennessy
      SFTP_CREDENTIALS: ${self:provider.stage}-sftp_credentials
    role: TitleSFTPToS3ToAriaRole
    layers: 
      - {Ref: RequestsLayerLambdaLayer}
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-paramiko:13
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-PyMuPDF:8
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/title_sftp_to_s3_to_aria.zip
      patterns:
        - "!**/*"
        - src/title_sftp_to_s3_to_aria/**
        
  orchestrator_downloader:
    name: snd-hen-orchestrator_downloader
    handler: lambda_function.lambda_handler
    runtime: python3.12
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ARIA_ENV: sandbox
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
    role: OrchestratorDownloaderRole
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/orchestrator_downloader.zip
      patterns:
        - "!**/*"
        - src/orchestrator_downloader/**

  reconciliate:
    name: snd-hen-reconciliate
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      ARIA_ENV: sandbox
      BRE_HANDLER_LAMBDA: ${self:provider.stage}-bre_handler
      BUCKET: ${self:provider.stage}-bucket
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
      REPORT_TO_ARIA_LAMBDA: ${self:provider.stage}-report_to_aria
    role: ReconciliateRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pydantic:14
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/reconciliate.zip
      patterns:
        - "!**/*"
        - src/reconciliate/**

  orchestrator_download_update:
    name: snd-hen-orchestrator_download_update
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ARIA_ENV: sandbox
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      REPORT_EMAIL: <EMAIL>
      SUPPORT_EMAIL: <EMAIL>
      ARIA_EMAIL: <EMAIL>
      REPORT_TO_ARIA_LAMBDA: ${self:provider.stage}-report_to_aria
    role: OrchestratorDownloadUpdateRole
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: MsalLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/orchestrator_download_update.zip
      patterns:
        - "!**/*"
        - src/orchestrator_download_update/**

  selenium_downloader:
    name: snd-hen-selenium_downloader
    image: 
      name: selenium-downloader-latest
    memorySize: 1024
    timeout: 900
    architecture: x86_64
    role: SeleniumDownloaderRole
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    environment:
      ENV: ${self:provider.stage}
      DB_NAME: snd_hennessy

  reevaluate:
    name: snd-hen-reevaluate
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      ARIA_ENV: sandbox
      BRE_HANDLER_LAMBDA: ${self:provider.stage}-bre_handler
      BUCKET: ${self:provider.stage}-bucket
    role: ReevaluateRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pydantic:14
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/reevaluate.zip
      patterns:
        - "!**/*"
        - src/reevaluate/**

  python_handler:
    name: snd-hen-python_handler
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: PythonHandlerRole
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/python_handler.zip
      patterns:
        - "!**/*"
        - external_modules/orchestrator_utilities/processes/rpa_processes_handler/**
    events:
      - httpApi:
          path: /${self:provider.stage}/python_handler
          method: post
          authorizer:
            name: lambda_authorizer
            type: request

  loading_wi_report:
    name: snd-hen-loading_wi_report
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      ARIA_ENV: sandbox
      REPORTER_EMAIL: <EMAIL>
      REPORTS_EMAIL: <EMAIL> #<EMAIL>
      BCC_EMAIL: <EMAIL>,<EMAIL>
    role: LoadingWiReportRole
    layers:
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: MsalLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pydantic:14
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/loading_wi_report.zip
      patterns:
        - "!**/*"
        - src/loading_wi_report/**

  error_wi_report:
    name: snd-hen-error_wi_report
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      ARIA_ENV: sandbox
      REPORTER_EMAIL: <EMAIL>
      REPORTS_EMAIL: <EMAIL> #<EMAIL>
      BCC_EMAIL: <EMAIL>,<EMAIL>
      MONGO_DATABASE: snd_hennessy  
      RECEIVER_KEY: ${self:provider.stage}_invoke # hennesy
    role: ErrorWiReportRole
    layers:
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - {Ref: MsalLayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pydantic:14
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/error_wi_report.zip
      patterns:
        - "!**/*"
        - src/error_wi_report/**

  report_loaded_data:
    name: snd-hen-report_loaded_data
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      ARIA_ENV: sandbox
      REPORTER_EMAIL: <EMAIL>
      REPORTS_EMAIL: <EMAIL>
      BCC_EMAIL: <EMAIL>,<EMAIL>
      MONGO_DATABASE: snd_hennessy  
    role: ReportLoadedDataRole
    layers:
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: MsalLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-jinja2:6
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/report_loaded_data.zip
      patterns:
        - "!**/*"
        - src/report_loaded_data/**

  queues_handler:
    name: snd-hen-queues_handler
    handler: lambda_function.lambda_handler
    events:
      - httpApi:
          path: /${self:provider.stage}/queues_handler
          method: post
          authorizer:
            name: lambda_authorizer
            type: request
    runtime: python3.11
    architecture: x86_64
    memorySize: 128
    timeout: 180
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: QueuesHandlerRole
    layers:
      - {Ref: PyMongoAWSLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/queues_handler.zip
      patterns:
        - "!**/*"
        - src/queues_handler/**

  report_pages_not_used_in_titles:
    name: snd-hen-report_pages_not_used_in_titles
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
      ARIA_ENV: sandbox
      REPORTER_EMAIL: <EMAIL>
      REPORTS_EMAIL: <EMAIL>
      BCC_EMAIL: <EMAIL>,<EMAIL>
      MONGO_DATABASE: snd_hennessy  
    role: ReportPagesNotUsedInTitlesRole
    layers:
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: MsalLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-jinja2:6
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/report_pages_not_used_in_titles.zip
      patterns:
        - "!**/*"
        - src/report_pages_not_used_in_titles/**
        
  secrets_handler:
    name: snd-hen-secrets_handler
    handler: lambda_function.lambda_handler
    events:
      - httpApi:
          path: /${self:provider.stage}/secrets_handler
          method: post
          authorizer:
            name: lambda_authorizer
            type: request
    runtime: python3.11
    architecture: x86_64
    memorySize: 128
    timeout: 180
    ephemeralStorageSize: 512
    environment:
      ENV: ${self:provider.stage}
    role: SecretsHandlerRole
    layers:
      # - {Ref: Boto3LayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/secrets_handler.zip
      patterns:
        - "!**/*"
        - src/secrets_handler/**

  report_to_aria:
    name: snd-hen-report_to_aria
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ARIA_ENV: sandbox
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      GROUP_NAME: reports
      ARIA_APP_ID: cf44b991-1dda-439c-a9f8-bb17e02f8635
      REYNOLS_REPORT_FOLDER: reynols_reports
    role: ReportToAriaRole
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - {Ref: RequestsLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
      - {Ref: OpenpyxlLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/report_to_aria.zip
      patterns:
        - "!**/*"
        - src/report_to_aria/**
    events:
      - httpApi:
          path: /${self:provider.stage}/report_to_aria
          method: post
          authorizer:
            name: lambda_authorizer
            type: request

  load_pricing_guide:
    name: snd-hen-load_pricing_guide
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      REYNOLS_REPORT_FOLDER: reynols_reports
      ARIA_ENV: sandbox
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
      PRICING_GUIDE_FOLDER: invoices_information
    role: PricingGuideRole
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p312-PyMuPDF:3
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/load_pricing_guide.zip
      patterns:
        - "!**/*"
        - src/load_pricing_guide/**

  load_pricing_guide_extractor:
    name: snd-hen-load_pricing_guide_extractor
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      ARIA_ENV: sandbox
      PDF_PROCESSER_LAMBDA: ${self:provider.stage}-pdf_utils
      LLM_MESSENGER_LAMBDA: ${self:provider.stage}-llm_messenger
      BUCKET_FOLDER: textract_operations
    role: PricingGuideExtractorRole
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/load_pricing_guide_extractor.zip
      patterns:
        - "!**/*"
        - src/load_pricing_guide_extractor/**

  pre_stock_in_vins:
    name: snd-hen-pre_stock_in_vins
    handler: lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    environment:
      BUCKET: ${self:provider.stage}-bucket
      ENV: ${self:provider.stage}
      MONGO_DATABASE: snd_hennessy
      NEW_VEHICLES_REPORTS_FOLDER: new_vehicles_reports
    role: PreStockInVinsRole
    layers: 
      # - {Ref: Boto3LayerLambdaLayer}
      - {Ref: PyMongoAWSLayerLambdaLayer}
      - arn:aws:lambda:us-east-1:************:layer:Klayers-p311-pandas:18
    vpc:
      securityGroupIds:
        - sg-0073e23cc9281b4f2
        - sg-0b48d49b8580b9bee
      subnetIds:
        - subnet-08d9a7a9cc266212d
        - subnet-03a91c27ea653680b
    package:
      individually: true
      artifact: artifacts/pre_stock_in_vins.zip
      patterns:
        - "!**/*"
        - src/pre_stock_in_vins/**