# This module contains utility functions for interacting with AWS services using the Boto3 library.

import json
import boto3
from botocore.exceptions    import ClientError
from bson                   import ObjectId
from datetime               import datetime
from urllib.parse import urlparse
import os

def get_secret(secret_name, return_json=True):
    """
    This function retrieves the secret value from AWS Secrets Manager.
    """
    region_name = "us-east-1"

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )

    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name
        )
    except ClientError as e:
        print(f"An error occurred while retrieving the secret value for {secret_name}: {e}")
        raise e

    secret = get_secret_value_response['SecretString']
    if return_json:
        return json.loads(secret)

    return secret

def get_latest_files(bucket_name, folder_path):
    """Gets the metadata of the latest CSV file from the given S3 bucket folder."""
    s3_client = boto3.client('s3')
    
    # List CSV files in the specified folder
    response = s3_client.list_objects_v2(
        Bucket=bucket_name,
        Prefix=folder_path
    )
    
    # Check if any files were found
    if 'Contents' not in response:
        return None
    
    # Filter the CSV files
    files = [file for file in response['Contents'] if file['Key'].endswith('.pdf')]
    
    if not files:
        return None
    
    # Sort files by last modified date (descending)
    files.sort(key=lambda x: x['LastModified'], reverse=True)
    
    # Return the metadata of the latest file

    return files


def post_to_s3(bucket, key, filename, file_path):
    # Upload the log message to S3
    s3 = boto3.client('s3', region_name='us-east-1')

    # Upload the log file to S3
    key_tmp = f"{key}/{filename}"
    s3.upload_file(file_path, bucket, key_tmp)

    presigned_url = s3.generate_presigned_url(
            'get_object', 
            Params={'Bucket': bucket, 'Key': key_tmp},
            ExpiresIn=(60 * 60 * 24 * 7)
        )
    
    return presigned_url



def download_file_from_s3(bucket_name, file_metadata):
    """Downloads the latest CSV file from S3 to /tmp/."""
    if file_metadata is None:
        return None
    
    s3_client = boto3.client('s3')
    
    # Download the latest CSV file to /tmp/
    download_path = os.path.join('/tmp', os.path.basename(file_metadata['Key']))
    
    s3_client.download_file(bucket_name, file_metadata['Key'], download_path)
    
    return download_path

def json_encoder(o):
    if isinstance(o, ObjectId):
        return str(o)
    if isinstance(o, datetime):
        return o.strftime('%Y-%m-%dT%H:%M:%S')
    return o

def trigger_lambda(function_name, payload):
    # Create an AWS Lambda client
    client = boto3.client('lambda', region_name='us-east-1')

    # Define the parameters for the async invocation
    response = client.invoke(
        FunctionName=function_name,
        InvocationType='Event',
        Payload=(
            json.dumps(payload, default=json_encoder)
        ).encode('utf-8')
    )

    return response
