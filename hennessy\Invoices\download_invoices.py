import vendors
from config import DefaultConfig

import os

def get_parameters():
    """to be replaced by secret manager"""
    return DefaultConfig.get_parameters()

def main(store, vins, brand=""):
    params = get_parameters()
    if store == 'FOR':
        store_site = vendors.FORD()
        store_site.login(params['USER_FOR'],params['PASSWORD_FOR'])
        return store_site.download_vins(vins)
    elif store == 'LOA':
        store_site = vendors.LEXUS(params['email_client_id'], params['email_client_secret'], params['email_tenant_id'], params['email_user_id'])
        store_site.login(params['USER_LOA'],params['PASSWORD_LOA'])
        return store_site.download_vins(vins)
    elif store == 'LOG':
        store_site = vendors.LEXUS(params['email_client_id'], params['email_client_secret'], params['email_tenant_id'], params['email_user_id'])
        store_site.login(params['USER_LOG'], params['PASSWORD_LOG'])
        return store_site.download_vins(vins)
    elif store in 'JLRN':
        store_site = vendors.JLR(brand)
        store_site.login(params['USER_JLRN'], params['PASSWORD_JLRN'])
        return store_site.download_vins(vins)
    elif store == 'POR':
        store_site = vendors.PORSCHE()
        store_site.login(params['USER_POR'], params['PASSWORD_POR'], params['MFA_POR'])
        return store_site.download_vins(vins)
    elif store == 'PNW':
        store_site = vendors.PORSCHE()
        store_site.login(params['USER_PNW'], params['PASSWORD_PNW'], params['MFA_PNW'])
        return store_site.download_vins(vins)


if __name__ == "__main__":
    test_map = {
        'FOR': {
            'vins': [
                "1FMEE1BP6RLB49429",
                "1FMDE1BH1RLB52498",
                "1FMDE8BH0RLB52923",
                "1FMDE1BHXRLB54394",
                "1FTER4KHXRLE75060",
                "3FMTK3R75RMA30562",
                "3FMTK3SU9RMA40379",
                "3FMTK1R47RMA51665",
                "3FMTK3SU7RMA52224",
                "1FT6W5L76RWG31487",
                "1FA6P8TH9S5101488",
                "1FA6P8R07S5500773",
                "1FTFW5L88SFA02893",
                "1FTFW5LD6SFA13856",
                "1FTFW5LD3SFA20960"
            ],
            'brand': 'Ford'
        },
        'LOA': {
            'vins': [
                "5TDAAAA64RS011992",
                "5TDABAB63RS006374",
                "5TDAAAA62RS011991",
                "5TDAAAA60RS011987",
                "JTHMPAAY9RA110879",
                "JTJTBCDX5R5031497",
                "JTJVBCDX5R5032190",
                "JTJTBCDX6R5032920",
                "JTJTBCDX3R5032339",
                "JTJVBCDX6R5032702",
                "JTJTBCDX0R5033254",
                "JTJTBCDXXR5032385",
                "JTJVBCDX0R5032937",
                "2T2GKCEZ8SC044382",
                "2T2GDCAZ3SC024430",
                "2T2BBMCAXSC071595"
            ],
            'brand': 'Lexus'
        },
        'LOG': {
            'vins': [
                'JTHMPAAY9RA110879',
                '5TDABAB63RS006374'
            ],
            'brand': 'Lexus'
        },
        'JLRN': {
            'vins': [
                "SALE27EU2S2382164",
                "SALE27EU5S2402679",
                "SALE27EU5S2404822",
                "SALEWEEEXS2405515",
                "SALEWEEE8S2407022",
                "SALEJ7EU2S2407454",
                "SALE2EEU6S2407491",
                "SALK19F43SA284234",
                "SAL1Z9F91SA445941",
                "SAL1L9E94SA450389",
                "SAL1L9E49SA451459",
                "SAL1L9E44SA451496",
                "SADCT2EX5SA753918",
                "SADCT2EX1SA754015"
            ],
            'brand': 'Land Rover'
        },
        'POR': {
            'vins': [
                'WP1AA2XA5RL003687',
                'WP1AA2AY2SDA03591'
            ],
            'brand': 'Porsche'
        },
        'PNW': {
            'vins': [''],
            'brand': 'Porsche'
        }
    }

    BRAND_PER_STORE = {
        'FOR': 'Ford',
        'LOA': 'Lexus',
        'LOG': 'Lexus',
        'JLRN': 'Land Rover',
        'POR': 'Porsche',
        'PNW': 'Porsche'
    }

    for store in test_map:
            print(f"Testing {store}...")
            main(store, test_map[store][:15], BRAND_PER_STORE.get(store, ""))
